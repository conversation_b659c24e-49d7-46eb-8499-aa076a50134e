<?php
// Set The Title
$category_header = $title = 'Software Lot Numbers Management';
$menu = 'software_lot_numbers';
?>

@extends('layouts.admin.master')
@section('title', $title)
@section('styles')
<style>
    /* BardAccess Unified Design System - Software Lot Numbers */

    /* Search Form - Standard admin style */
    .search-form {
        margin-bottom: 20px;
        background: #f5f5f5;
        padding: 15px;
        border: 1px solid #ddd;
    }

    /* Filter Row - Simplified */
    .filter-row {
        margin-bottom: 15px;
    }
    .filter-row .form-group {
        margin-bottom: 10px;
    }
    .filter-row .form-group label {
        font-weight: normal;
        color: #4A5C58;
        margin-bottom: 5px;
    }

    /* Action Buttons - Standard layout */
    .action-buttons-header {
        margin-bottom: 20px;
        padding: 10px 0;
    }
    .action-buttons-header .btn {
        margin-right: 10px;
        margin-bottom: 5px;
    }

    /* Table Styling - Standard admin */
    .table-responsive {
        border: 1px solid #dfdfdf;
        background: white;
    }
    .table th, .table td {
        padding: 10px;
        vertical-align: middle;
        border-bottom: 1px solid #dfdfdf;
    }
    .table th {
        background-color: #f8f9fa;
        font-weight: bold;
        border-bottom: 1px solid #111;
        text-align: left;
    }

    /* Pagination - Standard */
    .pagination {
        margin-top: 20px;
        text-align: center;
    }

    /* Bulk Actions - Simplified */
    .bulk-actions {
        margin-top: 20px;
        padding: 15px;
        background-color: #f5f5f5;
        border: 1px solid #ddd;
    }
    .bulk-actions .form-group {
        margin-bottom: 15px;
    }
    .bulk-actions h5 {
        color: #4A5C58;
        font-weight: bold;
        margin-bottom: 15px;
    }

    /* Selection styling */
    .selection-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 15px;
        border-radius: 4px;
    }

    .selected-count-text {
        font-weight: bold;
    }

    /* Highlight selected rows */
    tr.selected-row {
        background-color: #e8f4ff !important;
    }

    /* Dropdown styling */
    .dropdown-item {
        padding: 8px 20px;
    }

    .dropdown-item:hover {
        background-color: #f8f9fa;
    }

    /* Search input styling */
    .input-group-text {
        background-color: #f8f9fa;
        border-right: none;
    }

    .input-group .form-control {
        border-left: none;
    }

    .input-group .form-control:focus {
        box-shadow: none;
        border-color: #ced4da;
    }

    .input-group .form-control:focus + .input-group-append .input-group-text,
    .input-group .form-control:focus ~ .input-group-prepend .input-group-text {
        border-color: #ced4da;
    }

    /* Form control styling */
    .form-control {
        border-radius: 6px;
        border: 1px solid #ced4da;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    }

    /* Modern UI Improvements */
    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* Table hover effects */
    .table-hover tbody tr:hover {
        background-color: #f1f8ff;
        transition: background-color 0.2s ease;
    }

    /* Card-like sections */
    .card-section {
        background: white;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }

    /* Improved spacing */
    .main-content {
        padding: 20px;
    }

    /* Better visual hierarchy */
    h1 {
        color: #495057;
        font-weight: 600;
        margin-bottom: 20px;
    }

    /* Status badges - Professional styling */
    .status-badge {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-active {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .status-inactive {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    /* Action buttons in table */
    .action-buttons {
        display: flex;
        gap: 4px;
        align-items: center;
    }

    /* Responsive improvements */
    @media (max-width: 768px) {
        .filter-row {
            flex-direction: column;
        }

        .filter-row .form-group {
            min-width: 100%;
        }

        .action-buttons-header {
            flex-direction: column;
            align-items: stretch;
        }

        .action-buttons-header .btn,
        .action-buttons-header .btn-group {
            margin-bottom: 10px;
        }
    }

        .search-grid {
            grid-template-columns: 1fr;
        }

        .search-actions {
            flex-direction: column;
        }

        .search-actions .btn {
            width: 100%;
        }

        .modern-table {
            font-size: 12px;
        }

        .modern-table th,
        .modern-table td {
            padding: 12px 8px;
        }

        .action-buttons {
            flex-direction: column;
            gap: 4px;
        }
    }

    /* Loading States */
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    }

    .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .export-dropdown .dropdown-menu {
        border-radius: 8px;
        border: none;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        min-width: 180px;
    }

    .export-dropdown .dropdown-menu li a {
        padding: 12px 20px;
        font-size: 14px;
        color: #5a5c69;
        transition: all 0.2s ease;
        text-decoration: none;
    }

    .export-dropdown .dropdown-menu li a:hover {
        background: #f8f9fc;
        color: #4e73df;
        transform: translateX(5px);
        text-decoration: none;
    }

    .export-dropdown .dropdown-menu li a i {
        width: 18px;
        margin-right: 10px;
        color: #858796;
    }

    /* Search Panel */
    .panel {
        background: white;
        border: 1px solid #e3e6f0;
        border-radius: 12px;
        margin-bottom: 25px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }

    .panel-heading {
        background: linear-gradient(135deg, #f8f9fc 0%, #e9ecef 100%);
        border-bottom: 1px solid #e3e6f0;
        border-radius: 12px 12px 0 0;
        padding: 20px 25px;
    }

    .panel-title a {
        color: #5a5c69;
        text-decoration: none;
        font-weight: 600;
        font-size: 16px;
    }

    .panel-title a:hover {
        color: #4e73df;
        text-decoration: none;
    }

    .panel-body {
        padding: 25px;
    }

    .badge {
        background: #4e73df;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 11px;
        margin-left: 10px;
    }

    /* Table Styling */
    .table-responsive {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        background: white;
        margin-top: 20px;
    }

    .table {
        margin-bottom: 0;
        font-size: 14px;
    }

    .table thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        font-weight: 600;
        padding: 18px 15px;
        text-transform: uppercase;
        font-size: 12px;
        letter-spacing: 0.5px;
    }

    .table tbody tr {
        transition: all 0.2s ease;
    }

    .table tbody tr:hover {
        background-color: #f8f9fc;
        transform: scale(1.005);
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .table tbody td {
        padding: 15px;
        vertical-align: middle;
        border-top: 1px solid #e3e6f0;
    }

    /* Status Labels */
    .label {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        display: inline-block;
        margin: 2px;
    }

    .label-success {
        background: linear-gradient(135deg, #1cc88a, #13855c);
        color: white;
        border: none;
    }

    .label-info {
        background: linear-gradient(135deg, #36b9cc, #258391);
        color: white;
        border: none;
    }

    .label-default {
        background: linear-gradient(135deg, #858796, #5a5c69);
        color: white;
        border: none;
    }

    .label-warning {
        background: linear-gradient(135deg, #f6c23e, #dda20a);
        color: white;
        border: none;
    }

    /* Action Buttons */
    .table_icon {
        display: flex;
        gap: 8px;
        align-items: center;
        justify-content: center;
    }

    .table_icon a,
    .table_icon button {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        border: none;
        font-size: 14px;
        text-decoration: none;
    }

    .table_icon .btn-primary {
        background: linear-gradient(135deg, #4e73df, #224abe);
        color: white !important;
    }

    .table_icon .btn-primary:hover {
        background: linear-gradient(135deg, #2e59d9, #1e3a8a);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(78, 115, 223, 0.4);
        color: white !important;
        text-decoration: none;
    }

    .table_icon .btn-info {
        background: linear-gradient(135deg, #36b9cc, #258391);
        color: white !important;
    }

    .table_icon .btn-info:hover {
        background: linear-gradient(135deg, #2c9faf, #1e6b73);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(54, 185, 204, 0.4);
        color: white !important;
        text-decoration: none;
    }

    .table_icon .status-btn {
        background: linear-gradient(135deg, #1cc88a, #13855c);
        color: white !important;
        cursor: pointer;
    }

    .table_icon .status-btn:hover {
        background: linear-gradient(135deg, #17a673, #0e6b47);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(28, 200, 138, 0.4);
        color: white !important;
    }

    /* Pagination */
    .pagination-wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 25px;
        padding: 20px 25px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }

    .pagination-info {
        color: #858796;
        font-size: 14px;
        font-weight: 500;
    }

    .pagination {
        margin: 0;
    }

    .page-link {
        border-radius: 8px;
        margin: 0 3px;
        border: 1px solid #e3e6f0;
        color: #4e73df;
        padding: 10px 15px;
        font-weight: 500;
        transition: all 0.2s ease;
        text-decoration: none;
    }

    .page-link:hover {
        background: #4e73df;
        border-color: #4e73df;
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(78, 115, 223, 0.3);
        text-decoration: none;
    }

    .page-item.active .page-link {
        background: linear-gradient(135deg, #4e73df, #224abe);
        border-color: #4e73df;
        color: white;
        box-shadow: 0 2px 8px rgba(78, 115, 223, 0.3);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .header-main {
            flex-direction: column;
            text-align: center;
        }

        .header-actions {
            flex-direction: column;
            width: 100%;
        }

        .header-actions .btn {
            width: 100%;
            margin-bottom: 10px;
        }

        .pagination-wrapper {
            flex-direction: column;
            gap: 15px;
        }

        .table_icon {
            flex-direction: column;
            gap: 5px;
        }
    }

    @if($softwareLotNumbers->count() < 100)
    .dataTables_paginate {
        display: none;
    }
    @endif
</style>
@stop

@section('content')
<div id="content_hldr" class="">
    <div id="cat_title_hldr">
        <div id="cat_title">
            <div id="end_cap">
                <h1>{!! $title !!}</h1>
            </div>
        </div>
    </div>

    <!-- INCLUDE THE SIDEBAR -->
    @include('admin/elabeling/includes/sidebar')

    <div id="content">
        <!--pagecontent-->
        <div id="pagecontent">
            <div id="main">
                <!-- Action Buttons - Standard admin layout -->
                <div class="action-buttons-header">
                    <a href="{{ route('elabeling.software.lot.numbers.create') }}" class="btn btn-primary">
                        <i class="fa fa-plus"></i> Add New Lot Number
                    </a>
                    <a href="{{ route('elabeling.software.lot.numbers.analytics') }}" class="btn btn-info">
                        <i class="fa fa-bar-chart"></i> View Analytics
                    </a>
                    <div class="btn-group">
                        <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fa fa-download"></i> Export <span class="caret"></span>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a href="#" onclick="exportData('csv')"><i class="fa fa-file-text-o"></i> Export as CSV</a></li>
                            <li><a href="#" onclick="exportData('excel')"><i class="fa fa-file-excel-o"></i> Export as Excel</a></li>
                            <li><a href="#" onclick="exportData('pdf')"><i class="fa fa-file-pdf-o"></i> Export as PDF</a></li>
                        </ul>
                    </div>
                    <div class="btn-group">
                        <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fa fa-cog"></i> More Actions <span class="caret"></span>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a href="#" data-toggle="modal" data-target="#importModal">
                                <i class="fa fa-upload"></i> Import Data</a></li>
                            <li role="separator" class="divider"></li>
                            <li><a href="#" data-toggle="modal" data-target="#settingsModal">
                                <i class="fa fa-cog"></i> Settings</a></li>
                        </ul>
                    </div>
                </div>

                <!-- CHECK FOR FLASHED MESSAGE -->
                @include('flash::message')

                <!-- Search Form - Consistent with Device Matrix -->
                <div class="search-form">
                    <form action="{{ route('elabeling.software.lot.numbers.index') }}" method="GET">
                        <div class="filter-row">
                            <div class="form-group">
                                <label for="search">Search</label>
                                <input type="text" name="search" id="search" class="form-control"
                                       placeholder="Lot number, part number, product code..."
                                       value="{{ $search }}">
                            </div>

                            <div class="form-group">
                                <label for="status">Status</label>
                                <select name="status" id="status" class="form-control">
                                    <option value="">Active Only</option>
                                    <option value="inactive" {{ $status == 'inactive' ? 'selected' : '' }}>Inactive Only</option>
                                    <option value="all" {{ $status == 'all' ? 'selected' : '' }}>All Records</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="resource_type">Resource Type</label>
                                <select name="resource_type" id="resource_type" class="form-control">
                                    <option value="">All Types</option>
                                    @foreach($resourceTypes as $type)
                                        <option value="{{ $type }}" {{ $resourceType == $type ? 'selected' : '' }}>
                                            {{ $type }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="per_page">Per Page</label>
                                <select name="per_page" id="per_page" class="form-control">
                                    <option value="15" {{ $perPage == 15 ? 'selected' : '' }}>15</option>
                                    <option value="25" {{ $perPage == 25 ? 'selected' : '' }}>25</option>
                                    <option value="50" {{ $perPage == 50 ? 'selected' : '' }}>50</option>
                                    <option value="100" {{ $perPage == 100 ? 'selected' : '' }}>100</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="date_from">Date From</label>
                                <input type="date" name="date_from" id="date_from" class="form-control" value="{{ $dateFrom }}">
                            </div>

                            <div class="form-group">
                                <label for="date_to">Date To</label>
                                <input type="date" name="date_to" id="date_to" class="form-control" value="{{ $dateTo }}">
                            </div>

                            <div class="form-group">
                                <label for="sort_by">Sort By</label>
                                <select name="sort_by" id="sort_by" class="form-control">
                                    <option value="created_at" {{ $sortBy == 'created_at' ? 'selected' : '' }}>Date Created</option>
                                    <option value="updated_at" {{ $sortBy == 'updated_at' ? 'selected' : '' }}>Date Modified</option>
                                    <option value="lot_number" {{ $sortBy == 'lot_number' ? 'selected' : '' }}>Lot Number</option>
                                    <option value="part_number" {{ $sortBy == 'part_number' ? 'selected' : '' }}>Part Number</option>
                                    <option value="product_name" {{ $sortBy == 'product_name' ? 'selected' : '' }}>Product Name</option>
                                    <option value="manufacture_date" {{ $sortBy == 'manufacture_date' ? 'selected' : '' }}>Manufacture Date</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="sort_order">Order</label>
                                <select name="sort_order" id="sort_order" class="form-control">
                                    <option value="desc" {{ $sortOrder == 'desc' ? 'selected' : '' }}>Desc</option>
                                    <option value="asc" {{ $sortOrder == 'asc' ? 'selected' : '' }}>Asc</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fa fa-search"></i> Search
                                    </button>
                                    <a href="{{ route('elabeling.software.lot.numbers.index') }}" class="btn btn-secondary">
                                        <i class="fa fa-refresh"></i> Reset
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                @if ($softwareLotNumbers->count() > 0)

                <div id="statusMessage" class="alert alert-success" style="display:none;margin-top:30px;"></div>

                <!-- Modern Data Table Container -->
                <div class="data-table-container">
                    <div class="table-header">
                        <h3 class="table-title">
                            <i class="fa fa-table"></i> Software Lot Numbers
                            <span style="font-size: 14px; font-weight: normal; color: #6c757d;">
                                ({{ $softwareLotNumbers->total() }} total records)
                            </span>
                        </h3>
                    </div>

                    <!-- Bulk Actions Panel -->
                    <div class="bulk-actions" id="bulkActionsPanel" style="display: none; background: #667eea; color: white; padding: 15px 30px; border-bottom: 1px solid #dee2e6;">
                        <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 15px;">
                            <div style="display: flex; align-items: center; gap: 20px;">
                                <label style="display: flex; align-items: center; gap: 8px; margin: 0;">
                                    <input type="checkbox" id="selectAll"> Select All
                                </label>
                                <span><span id="selectedCount">0</span> records selected</span>
                            </div>
                            <div style="display: flex; gap: 10px;">
                                    <i class="fa fa-check"></i> Activate Selected
                                </button>
                                <button type="button" class="btn btn-sm" style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3);" onclick="bulkAction('deactivate')">
                                    <i class="fa fa-pause"></i> Deactivate Selected
                                </button>
                                <button type="button" class="btn btn-sm" style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3);" onclick="bulkAction('export')">
                                    <i class="fa fa-download"></i> Export Selected
                                </button>
                                @if (Auth::user()->hasPermissionTo('eliterature delete'))
                                <button type="button" class="btn btn-sm" style="background: rgba(220,53,69,0.8); color: white; border: 1px solid rgba(220,53,69,0.9);" onclick="bulkAction('delete')">
                                    <i class="fa fa-trash"></i> Delete Selected
                                </button>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Modern Data Table -->
                    <div style="overflow-x: auto;">
                        <table class="modern-table" id="myTable">
                            <thead>
                                <tr>
                                    <th style="width: 50px;">
                                        <input type="checkbox" id="selectAllTable">
                                    </th>
                                    <th>Lot Number</th>
                                    <th>Part Number</th>
                                    <th>Product Code</th>
                                    <th>Product Name</th>
                                    <th>Manufacture Date</th>
                                    <th>Status</th>
                                    <th>System Links</th>
                                    @if (Auth::user()->hasPermissionTo('eliterature edit'))
                                    <th>Actions</th>
                                    @endif
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($softwareLotNumbers as $lotNumber)
                                <tr id="tr{{ $lotNumber->id }}">
                                    <td>
                                        <input type="checkbox" class="record-checkbox" value="{{ $lotNumber->id }}" data-id="{{ $lotNumber->id }}">
                                    </td>
                                    <td>
                                        <strong>{{ $lotNumber->lot_number }}</strong>
                                    </td>
                                    <td>{{ $lotNumber->part_number }}</td>
                                    <td>{{ $lotNumber->product_code }}</td>
                                    <td>{{ $lotNumber->product_name }}</td>
                                    <td>{{ $lotNumber->formatted_manufacture_date }}</td>
                                    <td>
                                        @if($lotNumber->deleted_at)
                                        <span class="status-badge status-inactive">Inactive</span>
                                        @else
                                        <span class="status-badge status-active">Active</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($lotNumber->has_jde_link || $lotNumber->has_mfgpro_link)
                                            <div>
                                                @if($lotNumber->has_jde_link)
                                                    <span class="label label-info" title="Linked to JDE System">JDE</span>
                                                @endif
                                                @if($lotNumber->has_mfgpro_link)
                                                    <span class="label label-primary" title="Linked to MfgPro System">MfgPro</span>
                                                @endif
                                            </div>
                                        @else
                                            <span class="label label-default" title="No cross-system links">None</span>
                                        @endif
                                    </td>
                                    @if (Auth::user()->hasPermissionTo('eliterature edit'))
                                    <td>
                                        <div class="action-buttons">
                                            <a href="{{ route('elabeling.software.lot.numbers.show', [$lotNumber->id]) }}"
                                               class="btn btn-sm btn-info" title="View Details">
                                                <i class="fa fa-eye"></i>
                                            </a>

                                            <a href="{{ route('elabeling.software.lot.numbers.edit', [$lotNumber->id]) }}"
                                               class="btn btn-sm btn-primary" title="Edit">
                                                <i class="fa fa-pencil"></i>
                                            </a>

                                            @if (Auth::user()->hasPermissionTo('eliterature delete'))
                                            <button type="button"
                                                    class="btn btn-sm {{ $lotNumber->deleted_at ? 'btn-success' : 'btn-danger' }}"
                                                    title="{{ $lotNumber->deleted_at ? 'Activate' : 'Deactivate' }}"
                                                    onclick="recordStatus('update', '{{ route('elabeling.software.lot.numbers.change.status', [$lotNumber->id]) }}', {id: {{ $lotNumber->id }}, _token: '{{ csrf_token() }}'})">
                                                <i class="fa fa-power-off"></i>
                                            </button>
                                            @endif
                                        </div>
                                    </td>
                                    @endif
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Standard Pagination -->
                <div class="pagination">
                    <p>
                        Showing {{ $softwareLotNumbers->firstItem() ?? 0 }} to {{ $softwareLotNumbers->lastItem() ?? 0 }}
                        of {{ $softwareLotNumbers->total() }} results
                    </p>
                    {{ $softwareLotNumbers->appends(request()->query())->links() }}
                </div>

                @else

                <br />
                <div class="alert alert-warning">
                    @if($search)
                    No software lot numbers found matching your search criteria.
                    @else
                    There are no software lot numbers found.
                    @endif
                </div>

                @endif

            </div>
        </div>
        <div id="return"><a href="#top">top</a></div>
    </div>
</div>
<div id="content_hldr_btm"></div>
@stop

@section('scripts')
<script type="text/javascript">
    $(document).ready(function() {
        // datatable for the chart
        $('#myTable').dataTable({
            "responsive": true,
            "paging": false,
            "info": false,
            "searching": false,
            "lengthMenu": [
                [100, 150, 200, -1],
                [100, 150, 200, "All"]
            ]
        });
    });

    function recordStatus(action, url, data) {
        $.ajax({
            url: url,
            type: 'POST',
            data: data,
            success: function(response) {
                if (response.success) {
                    $('#statusMessage').html(response.message).show().delay(3000).fadeOut();
                    // Reload the page to reflect changes
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                }
            },
            error: function(xhr) {
                alert('Error: ' + xhr.responseJSON.message);
            }
        });
    }

    function exportData(format) {
        // Get current search parameters
        var searchParams = new URLSearchParams(window.location.search);
        searchParams.set('format', format);

        // Build export URL with current filters
        var exportUrl = '{{ route("elabeling.software.lot.numbers.export") }}?' + searchParams.toString();

        // Open export in new window/tab
        window.open(exportUrl, '_blank');
    }

    // Bulk operations functionality
    $(document).ready(function() {
        // Handle select all checkboxes
        $('#selectAll, #selectAllTable').change(function() {
            var isChecked = $(this).is(':checked');
            $('.record-checkbox').prop('checked', isChecked);
            $('#selectAll').prop('checked', isChecked);
            $('#selectAllTable').prop('checked', isChecked);
            updateBulkActions();
        });

        // Handle individual checkbox changes
        $(document).on('change', '.record-checkbox', function() {
            updateBulkActions();
            updateSelectAllState();
        });

        function updateSelectAllState() {
            var totalCheckboxes = $('.record-checkbox').length;
            var checkedCheckboxes = $('.record-checkbox:checked').length;

            $('#selectAll, #selectAllTable').prop('checked', totalCheckboxes === checkedCheckboxes);
        }

        function updateBulkActions() {
            var selectedCount = $('.record-checkbox:checked').length;
            $('#selectedCount').text(selectedCount);

            if (selectedCount > 0) {
                $('#bulkActionsPanel').show();
            } else {
                $('#bulkActionsPanel').hide();
            }
        }
    });

    function bulkAction(action) {
        var selectedIds = [];
        $('.record-checkbox:checked').each(function() {
            selectedIds.push($(this).val());
        });

        if (selectedIds.length === 0) {
            alert('Please select at least one record.');
            return;
        }

        var confirmMessage = '';
        switch (action) {
            case 'activate':
                confirmMessage = 'Are you sure you want to activate ' + selectedIds.length + ' selected record(s)?';
                break;
            case 'deactivate':
                confirmMessage = 'Are you sure you want to deactivate ' + selectedIds.length + ' selected record(s)?';
                break;
            case 'delete':
                confirmMessage = 'Are you sure you want to permanently delete ' + selectedIds.length + ' selected record(s)? This action cannot be undone.';
                break;
            case 'export':
                // No confirmation needed for export
                break;
        }

        if (action !== 'export' && !confirm(confirmMessage)) {
            return;
        }

        // Show loading state
        $('#bulkActions button').prop('disabled', true);

        $.ajax({
            url: '{{ route("elabeling.software.lot.numbers.bulk.action") }}',
            type: 'POST',
            data: {
                action: action,
                selected_ids: selectedIds,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    $('#statusMessage').html(response.message).show().delay(3000).fadeOut();

                    if (action === 'export') {
                        // Handle export response - this would typically be a file download
                        // For now, just show success message
                    } else {
                        // Reload the page to reflect changes
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    }
                } else {
                    alert('Error: ' + response.message);
                }
            },
            error: function(xhr) {
                var errorMessage = 'An error occurred';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                alert('Error: ' + errorMessage);
            },
            complete: function() {
                $('#bulkActions button').prop('disabled', false);
            }
        });
    }
</script>
@stop
