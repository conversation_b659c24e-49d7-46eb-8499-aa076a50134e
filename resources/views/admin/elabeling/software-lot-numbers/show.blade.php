<?php
// Set The Title
$category_header = $title = 'View Software Lot Number';
$menu = 'software_lot_numbers';
?>

@extends('layouts.admin.master')
@section('title', $title)

@section('content')
<div id="content_hldr" class="">
    <div id="cat_title_hldr">
        <div id="cat_title">
            <div id="end_cap">
                <h1>{!! $title !!}</h1>
            </div>
        </div>
    </div>

    <!-- INCLUDE THE SIDEBAR -->
    @include('admin/elabeling/includes/sidebar')

    <div id="content">
        <!--pagecontent-->
        <div id="pagecontent">
            <div id="main">
                <h4 style="margin-top: 0px;">Software Lot Number: {{ $softwareLotNumber->lot_number }}</h4>

                <!-- CHECK FOR FLASHED MESSAGE -->
                @include('flash::message')

                <div class="row">
                    <div class="col-md-8">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h3 class="panel-title">Software Lot Number Details</h3>
                            </div>
                            <div class="panel-body">
                                <table class="table table-bordered">
                                    <tr>
                                        <th width="200">Lot Number:</th>
                                        <td>{{ $softwareLotNumber->lot_number }}</td>
                                    </tr>
                                    <tr>
                                        <th>Part Number:</th>
                                        <td>{{ $softwareLotNumber->part_number }}</td>
                                    </tr>
                                    <tr>
                                        <th>Product Code:</th>
                                        <td>{{ $softwareLotNumber->product_code ?: 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Product Name:</th>
                                        <td>{{ $softwareLotNumber->product_name ?: 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Reference:</th>
                                        <td>{{ $softwareLotNumber->ref ?: 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Resource Type:</th>
                                        <td>{{ $softwareLotNumber->resource_type ?: 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Manufacture Date:</th>
                                        <td>{{ $softwareLotNumber->formatted_manufacture_date ?: 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Sequence Number:</th>
                                        <td>{{ $softwareLotNumber->sequence_number ?: 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Status:</th>
                                        <td>
                                            @if($softwareLotNumber->deleted_at)
                                            <span class="label label-danger">Inactive</span>
                                            @else
                                            <span class="label label-success">Active</span>
                                            @endif
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="panel panel-info">
                            <div class="panel-heading">
                                <h3 class="panel-title">Record Information</h3>
                            </div>
                            <div class="panel-body">
                                <p><strong>ID:</strong> {{ $softwareLotNumber->id }}</p>
                                <p><strong>Created:</strong> {{ $softwareLotNumber->created_at->format('Y-m-d H:i:s') }}</p>
                                <p><strong>Last Updated:</strong> {{ $softwareLotNumber->updated_at->format('Y-m-d H:i:s') }}</p>
                                @if($softwareLotNumber->deleted_at)
                                <p><strong>Deactivated:</strong> {{ $softwareLotNumber->deleted_at->format('Y-m-d H:i:s') }}</p>
                                @endif
                            </div>
                        </div>
                        
                        <div class="panel panel-warning">
                            <div class="panel-heading">
                                <h3 class="panel-title">Actions</h3>
                            </div>
                            <div class="panel-body">
                                <a href="{{ route('elabeling.software.lot.numbers.edit', $softwareLotNumber->id) }}" class="btn btn-primary btn-block">
                                    <i class="fa fa-edit"></i> Edit Record
                                </a>
                                
                                @if (Auth::user()->hasPermissionTo('eliterature delete'))
                                <hr>
                                @if($softwareLotNumber->deleted_at)
                                <button class="btn btn-success btn-block" onclick="recordStatus('update', '{{ route('elabeling.software.lot.numbers.change.status', [$softwareLotNumber->id]) }}', {id: {{ $softwareLotNumber->id }}, _token: '{{ csrf_token() }}'})">
                                    <i class="fa fa-check"></i> Activate Record
                                </button>
                                @else
                                <button class="btn btn-warning btn-block" onclick="recordStatus('update', '{{ route('elabeling.software.lot.numbers.change.status', [$softwareLotNumber->id]) }}', {id: {{ $softwareLotNumber->id }}, _token: '{{ csrf_token() }}'})">
                                    <i class="fa fa-ban"></i> Deactivate Record
                                </button>
                                @endif
                                
                                <button class="btn btn-danger btn-block" onclick="if(confirm('Are you sure you want to permanently delete this software lot number?')) { document.getElementById('delete-form').submit(); }">
                                    <i class="fa fa-trash"></i> Delete Permanently
                                </button>
                                
                                <form id="delete-form" action="{{ route('elabeling.software.lot.numbers.destroy', [$softwareLotNumber->id]) }}" method="POST" style="display: none;">
                                    @csrf
                                    @method('DELETE')
                                </form>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Relationship Information -->
                <div class="row" style="margin-top: 30px;">
                    <div class="col-md-12">
                        <div class="panel panel-info">
                            <div class="panel-heading">
                                <h4 class="panel-title">
                                    <i class="fa fa-link"></i> Related Records & Cross-System Links
                                </h4>
                            </div>
                            <div class="panel-body">
                                @php
                                    $relationshipSummary = $softwareLotNumber->getRelationshipSummary();
                                @endphp

                                <div class="row">
                                    <div class="col-md-6">
                                        <h5><i class="fa fa-database"></i> Cross-System Integration</h5>
                                        <table class="table table-condensed">
                                            <tr>
                                                <td><strong>JDE System:</strong></td>
                                                <td>
                                                    @if($relationshipSummary['details']['has_jde_lot'])
                                                        <span class="label label-success">
                                                            <i class="fa fa-check"></i> Linked
                                                        </span>
                                                    @else
                                                        <span class="label label-default">
                                                            <i class="fa fa-times"></i> Not Linked
                                                        </span>
                                                    @endif
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>MfgPro System:</strong></td>
                                                <td>
                                                    @if($relationshipSummary['details']['has_mfgpro_lot'])
                                                        <span class="label label-success">
                                                            <i class="fa fa-check"></i> Linked
                                                        </span>
                                                    @else
                                                        <span class="label label-default">
                                                            <i class="fa fa-times"></i> Not Linked
                                                        </span>
                                                    @endif
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <h5><i class="fa fa-info-circle"></i> Integration Status</h5>
                                        <table class="table table-condensed">
                                            <tr>
                                                <td><strong>Cross-System Links:</strong></td>
                                                <td>
                                                    @if($relationshipSummary['has_cross_system_links'])
                                                        <span class="label label-success">Active</span>
                                                    @else
                                                        <span class="label label-warning">None</span>
                                                    @endif
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>System Usage:</strong></td>
                                                <td>
                                                    @if($relationshipSummary['is_actively_used'])
                                                        <span class="label label-info">In Use</span>
                                                    @else
                                                        <span class="label label-default">Standalone</span>
                                                    @endif
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>

                                @if($relationshipSummary['has_cross_system_links'])
                                    <div class="alert alert-info" style="margin-top: 15px;">
                                        <i class="fa fa-info-circle"></i>
                                        <strong>Integration Notice:</strong> This software lot number is linked to other systems.
                                        Changes to this record may affect related data in JDE and/or MfgPro systems.
                                    </div>
                                @endif

                                @if(!$relationshipSummary['is_actively_used'])
                                    <div class="alert alert-warning" style="margin-top: 15px;">
                                        <i class="fa fa-exclamation-triangle"></i>
                                        <strong>Standalone Record:</strong> This software lot number is not currently linked to other systems.
                                        Consider establishing cross-system links for better data integration.
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <br/>
                <div class="form-group" style="margin-top:20px;">
                    <a href="{{ route('elabeling.software.lot.numbers.edit', $softwareLotNumber->id) }}" class="btn btn-primary">
                        <i class="fa fa-edit"></i> Edit
                    </a>
                    <a href="{{ route('elabeling.software.lot.numbers.index') }}" class="btn btn-default">
                        <i class="fa fa-arrow-left"></i> Back to List
                    </a>
                </div>

            </div>
        </div>
        <div id="return"><a href="#top">top</a></div>
    </div>
</div>
<div id="content_hldr_btm"></div>
@stop

@section('scripts')
<script type="text/javascript">
    function recordStatus(action, url, data) {
        $.ajax({
            url: url,
            type: 'POST',
            data: data,
            success: function(response) {
                if (response.success) {
                    alert(response.message);
                    // Reload the page to reflect changes
                    location.reload();
                }
            },
            error: function(xhr) {
                alert('Error: ' + xhr.responseJSON.message);
            }
        });
    }
</script>
@stop
