<?php
// Set The Title
$category_header = $title = 'Add Software Lot Number';
$menu = 'software_lot_numbers';
?>

@extends('layouts.admin.master')
@section('title', $title)

@section('content')
<div id="content_hldr" class="">
    <div id="cat_title_hldr">
        <div id="cat_title">
            <div id="end_cap">
                <h1>{!! $title !!}</h1>
            </div>
        </div>
    </div>

    <!-- INCLUDE THE SIDEBAR -->
    @include('admin/elabeling/includes/sidebar')

    <div id="content">
        <!--pagecontent-->
        <div id="pagecontent">
            <div id="main">
                <h4 style="margin-top: 0px;">Add New Software Lot Number</h4>

                <!-- CHECK FOR FLASHED MESSAGE -->
                @include('flash::message')

                @if ($errors->any())
                <div class="alert alert-danger">
                    <ul>
                        @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
                @endif

                {!! Form::open(['route' => 'elabeling.software.lot.numbers.store', 'id' => 'form', 'role' => 'form']) !!}

                <div class="form-group">
                    {!! Form::label('lot_number', 'Lot Number *:', ['class' => '', 'style' => 'font-weight:900;']); !!}
                    {!! Form::text('lot_number', old('lot_number'), ['id' => 'lot_number', 'class' => 'default-value form-control active required', 'maxlength' => '25']); !!}
                </div>

                <div class="form-group">
                    {!! Form::label('part_number', 'Part Number *:', ['class' => '', 'style' => 'font-weight:900;']); !!}
                    {!! Form::text('part_number', old('part_number'), ['id' => 'part_number', 'class' => 'default-value form-control active required', 'maxlength' => '25']); !!}
                </div>

                <div class="form-group">
                    {!! Form::label('product_code', 'Product Code:', ['class' => '', 'style' => 'font-weight:900;']); !!}
                    {!! Form::text('product_code', old('product_code'), ['id' => 'product_code', 'class' => 'default-value form-control active', 'maxlength' => '25']); !!}
                </div>

                <div class="form-group">
                    {!! Form::label('product_name', 'Product Name:', ['class' => '', 'style' => 'font-weight:900;']); !!}
                    {!! Form::text('product_name', old('product_name'), ['id' => 'product_name', 'class' => 'default-value form-control active', 'maxlength' => '255']); !!}
                </div>

                <div class="form-group">
                    {!! Form::label('ref', 'Reference:', ['class' => '', 'style' => 'font-weight:900;']); !!}
                    {!! Form::text('ref', old('ref'), ['id' => 'ref', 'class' => 'default-value form-control active', 'maxlength' => '25']); !!}
                </div>

                <div class="form-group">
                    {!! Form::label('resource_type', 'Resource Type:', ['class' => '', 'style' => 'font-weight:900;']); !!}
                    @if(!empty($resourceTypes))
                    {!! Form::select('resource_type', ['' => 'Select Resource Type'] + $resourceTypes, old('resource_type'), ['id' => 'resource_type', 'class' => 'form-control']); !!}
                    @else
                    {!! Form::text('resource_type', old('resource_type'), ['id' => 'resource_type', 'class' => 'default-value form-control active', 'maxlength' => '25']); !!}
                    @endif
                </div>

                <div class="form-group">
                    {!! Form::label('manufacture_date', 'Manufacture Date:', ['class' => '', 'style' => 'font-weight:900;']); !!}
                    {!! Form::date('manufacture_date', old('manufacture_date'), ['id' => 'manufacture_date', 'class' => 'form-control']); !!}
                </div>

                <div class="form-group">
                    {!! Form::label('sequence_number', 'Sequence Number:', ['class' => '', 'style' => 'font-weight:900;']); !!}
                    {!! Form::number('sequence_number', old('sequence_number'), ['id' => 'sequence_number', 'class' => 'form-control', 'min' => '0']); !!}
                </div>

                <br/>

                <div class="form-group" style="margin-top:20px;">
                    {!! Form::submit('Add Software Lot Number', ['class' => 'btn btn-primary', 'title' => 'Add Software Lot Number']) !!}
                </div>

                <br>
                <div class="row"><span class="error">*Required</span></div>

                {!! Form::close() !!}
                        <div class="panel panel-info">
                            <div class="panel-heading">
                                <h3 class="panel-title">Help</h3>
                            </div>
                            <div class="panel-body">
                                <p><strong>Lot Number:</strong> The unique identifier for this software lot.</p>
                                <p><strong>Part Number:</strong> The part number associated with this software.</p>
                                <p><strong>Product Code:</strong> Optional product code for categorization.</p>
                                <p><strong>Product Name:</strong> Descriptive name of the software product.</p>
                                <p><strong>Reference:</strong> Additional reference information.</p>
                                <p><strong>Resource Type:</strong> Type of resource (if applicable).</p>
                                <p><strong>Manufacture Date:</strong> Date when the software was created/released.</p>
                                <p><strong>Sequence Number:</strong> Optional sequence number for ordering.</p>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <div id="return"><a href="#top">top</a></div>
    </div>
</div>
<div id="content_hldr_btm"></div>
@stop

@section('scripts')
<script type="text/javascript">
    $(document).ready(function() {
        // Form validation
        $('#form').on('submit', function(e) {
            var lotNumber = $('#lot_number').val().trim();
            var partNumber = $('#part_number').val().trim();
            
            if (lotNumber === '') {
                alert('Lot Number is required.');
                $('#lot_number').focus();
                e.preventDefault();
                return false;
            }
            
            if (partNumber === '') {
                alert('Part Number is required.');
                $('#part_number').focus();
                e.preventDefault();
                return false;
            }
        });
    });
</script>
@stop
