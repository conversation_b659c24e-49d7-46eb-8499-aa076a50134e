<?php
// Set The Title
$category_header = $title = 'Software Lot Numbers Analytics';
$menu = 'software_lot_numbers';
?>

@extends('layouts.admin.master')
@section('title', $title)

@section('styles')
<style>
    .metric-card {
        background: white;
        border: 1px solid #ddd;
        padding: 20px;
        margin-bottom: 20px;
    }

    .metric-number {
        font-size: 2.5em;
        font-weight: bold;
        color: #333;
        margin-bottom: 8px;
        line-height: 1;
    }

    .metric-label {
        color: #666;
        font-size: 14px;
        text-transform: uppercase;
        font-weight: 600;
    }

    .chart-container {
        background: white;
        border: 1px solid #ddd;
        padding: 20px;
        margin-bottom: 20px;
    }

    .chart-container h4 {
        color: #333;
        font-weight: 600;
        margin-bottom: 20px;
        font-size: 18px;
    }
</style>
@stop

@section('content')
<div id="content_hldr" class="">
    <div id="cat_title_hldr">
        <div id="cat_title">
            <div id="end_cap">
                <h1>{!! $title !!}</h1>
            </div>
        </div>
    </div>

    <!-- INCLUDE THE SIDEBAR -->
    @include('admin/elabeling/includes/sidebar')

    <div id="content">
        <!--pagecontent-->
        <div id="pagecontent">
            <div id="main">
                <h4 style="margin-top: 0px;"><i class="fa fa-bar-chart"></i> Analytics Dashboard</h4>

                @if(isset($error))
                <div class="alert alert-warning">
                    <i class="fa fa-exclamation-triangle"></i> {{ $error }}
                </div>
                @endif

                <!-- Key Metrics Row -->
                <div class="row">
                    <div class="col-md-3">
                        <div class="metric-card text-center">
                            <div class="metric-number">{{ number_format($metrics['total_lots']) }}</div>
                            <div class="metric-label">Total Lot Numbers</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card text-center">
                            <div class="metric-number">{{ number_format($metrics['active_lots']) }}</div>
                            <div class="metric-label">Active Records</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card text-center">
                            <div class="metric-number">{{ number_format($metrics['inactive_lots']) }}</div>
                            <div class="metric-label">Inactive Records</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card text-center">
                            <div class="metric-number">{{ number_format($metrics['recent_additions']) }}</div>
                            <div class="metric-label">Added This Month</div>
                        </div>
                    </div>
                </div>

                <!-- Active vs Inactive Ratio -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="chart-container">
                            <h5><i class="fa fa-pie-chart"></i> Active vs Inactive Distribution</h5>
                            <div style="margin-top: 20px;">
                                <div style="margin-bottom: 15px;">
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                        <span>Active ({{ $metrics['active_percentage'] }}%)</span>
                                        <span>{{ number_format($metrics['active_lots']) }}</span>
                                    </div>
                                    <div class="progress-bar-custom">
                                        <div class="progress-fill" style="width: {{ $metrics['active_percentage'] }}%; background: linear-gradient(90deg, #27ae60, #2ecc71);"></div>
                                    </div>
                                </div>
                                <div>
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                        <span>Inactive ({{ 100 - $metrics['active_percentage'] }}%)</span>
                                        <span>{{ number_format($metrics['inactive_lots']) }}</span>
                                    </div>
                                    <div class="progress-bar-custom">
                                        <div class="progress-fill" style="width: {{ 100 - $metrics['active_percentage'] }}%; background: linear-gradient(90deg, #e74c3c, #c0392b);"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="chart-container">
                            <h5><i class="fa fa-bar-chart"></i> Resource Type Distribution</h5>
                            <div style="margin-top: 20px;">
                                @if($metrics['resource_type_distribution']->count() > 0)
                                    @foreach($metrics['resource_type_distribution'] as $type)
                                        @php
                                            $percentage = $metrics['total_lots'] > 0 ? round(($type->count / $metrics['total_lots']) * 100, 1) : 0;
                                        @endphp
                                        <div style="margin-bottom: 15px;">
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                                <span>{{ $type->resource_type ?: 'Unspecified' }}</span>
                                                <span>{{ number_format($type->count) }} ({{ $percentage }}%)</span>
                                            </div>
                                            <div class="progress-bar-custom">
                                                <div class="progress-fill" style="width: {{ $percentage }}%;"></div>
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <p class="text-muted">No resource type data available</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Monthly Trends -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="chart-container">
                            <h5><i class="fa fa-line-chart"></i> Monthly Creation Trends (Last 12 Months)</h5>
                            <div style="margin-top: 20px;">
                                @if($metrics['monthly_trends']->count() > 0)
                                    @php
                                        $maxCount = $metrics['monthly_trends']->max('count');
                                    @endphp
                                    @foreach($metrics['monthly_trends'] as $trend)
                                        @php
                                            $percentage = $maxCount > 0 ? ($trend->count / $maxCount) * 100 : 0;
                                        @endphp
                                        <div style="margin-bottom: 15px;">
                                            <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                                <span>{{ $trend->month_name }} {{ $trend->year }}</span>
                                                <span>{{ number_format($trend->count) }} records</span>
                                            </div>
                                            <div class="progress-bar-custom">
                                                <div class="progress-fill" style="width: {{ $percentage }}%;"></div>
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <p class="text-muted">No trend data available</p>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="chart-container">
                            <h5><i class="fa fa-clock-o"></i> Recent Activity</h5>
                            <div style="margin-top: 20px; max-height: 400px; overflow-y: auto;">
                                @if($metrics['recent_activity']->count() > 0)
                                    @foreach($metrics['recent_activity'] as $activity)
                                        <div class="recent-activity-item">
                                            <div>
                                                <strong>{{ $activity->lot_number }}</strong><br>
                                                <small class="text-muted">
                                                    @if($activity->updated_at && is_object($activity->updated_at))
                                                        {{ $activity->updated_at->diffForHumans() }}
                                                    @else
                                                        Unknown
                                                    @endif
                                                </small>
                                            </div>
                                            <div>
                                                @if($activity->deleted_at)
                                                    <span class="status-badge status-inactive">Inactive</span>
                                                @else
                                                    <span class="status-badge status-active">Active</span>
                                                @endif
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <p class="text-muted">No recent activity</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Top Product Codes -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="chart-container">
                            <h5><i class="fa fa-tags"></i> Top Product Codes</h5>
                            <div style="margin-top: 20px;">
                                @if($metrics['top_product_codes']->count() > 0)
                                    <div class="row">
                                        @foreach($metrics['top_product_codes'] as $index => $productCode)
                                            @php
                                                $percentage = $metrics['total_lots'] > 0 ? round(($productCode->count / $metrics['total_lots']) * 100, 1) : 0;
                                            @endphp
                                            <div class="col-md-6" style="margin-bottom: 15px;">
                                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                                    <span><strong>#{{ $index + 1 }}</strong> {{ $productCode->product_code }}</span>
                                                    <span>{{ number_format($productCode->count) }} ({{ $percentage }}%)</span>
                                                </div>
                                                <div class="progress-bar-custom">
                                                    <div class="progress-fill" style="width: {{ $percentage }}%;"></div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                @else
                                    <p class="text-muted">No product code data available</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <br/>
                <div class="form-group" style="margin-top:20px;">
                    <a href="{{ route('elabeling.software.lot.numbers.index') }}" class="btn btn-default">
                        <i class="fa fa-arrow-left"></i> Back to List
                    </a>
                </div>

            </div>
        </div>
        <div id="return"><a href="#top">top</a></div>
    </div>
</div>
<div id="content_hldr_btm"></div>
@stop

@section('scripts')
<script type="text/javascript">
    $(document).ready(function() {
        // Add any interactive features here
        console.log('Analytics dashboard loaded');
    });
</script>
@stop
