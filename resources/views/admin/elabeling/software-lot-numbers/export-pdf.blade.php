<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Software Lot Numbers Export</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        .export-info {
            margin-bottom: 20px;
            font-size: 10px;
            color: #666;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            font-size: 10px;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .status-active {
            color: #27ae60;
            font-weight: bold;
        }
        .status-inactive {
            color: #e74c3c;
            font-weight: bold;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .page-break {
            page-break-after: always;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Software Lot Numbers Export</h1>
        <h3>BardAccess - Elabeling System</h3>
    </div>

    <div class="export-info">
        <strong>Export Date:</strong> {{ date('Y-m-d H:i:s') }}<br>
        <strong>Total Records:</strong> {{ $data->count() }}<br>
        <strong>Active Records:</strong> {{ $data->whereNull('deleted_at')->count() }}<br>
        <strong>Inactive Records:</strong> {{ $data->whereNotNull('deleted_at')->count() }}
    </div>

    <table>
        <thead>
            <tr>
                <th>Lot Number</th>
                <th>Part Number</th>
                <th>Product Code</th>
                <th>Product Name</th>
                <th>Resource Type</th>
                <th>Manufacture Date</th>
                <th>Status</th>
                <th>Created Date</th>
            </tr>
        </thead>
        <tbody>
            @foreach($data as $index => $record)
                <tr>
                    <td>{{ $record->lot_number }}</td>
                    <td>{{ $record->part_number }}</td>
                    <td>{{ $record->product_code }}</td>
                    <td>{{ $record->product_name }}</td>
                    <td>{{ $record->resource_type }}</td>
                    <td>{{ $record->manufacture_date ? $record->manufacture_date->format('Y-m-d') : '' }}</td>
                    <td class="{{ $record->deleted_at ? 'status-inactive' : 'status-active' }}">
                        {{ $record->deleted_at ? 'Inactive' : 'Active' }}
                    </td>
                    <td>{{ $record->created_at->format('Y-m-d') }}</td>
                </tr>
                
                @if(($index + 1) % 25 == 0 && ($index + 1) < $data->count())
                    </tbody>
                    </table>
                    <div class="page-break"></div>
                    <table>
                        <thead>
                            <tr>
                                <th>Lot Number</th>
                                <th>Part Number</th>
                                <th>Product Code</th>
                                <th>Product Name</th>
                                <th>Resource Type</th>
                                <th>Manufacture Date</th>
                                <th>Status</th>
                                <th>Created Date</th>
                            </tr>
                        </thead>
                        <tbody>
                @endif
            @endforeach
        </tbody>
    </table>

    <div class="footer">
        <p>Generated by BardAccess Elabeling System | {{ date('Y-m-d H:i:s') }}</p>
        <p>This document contains {{ $data->count() }} software lot number records</p>
    </div>
</body>
</html>
