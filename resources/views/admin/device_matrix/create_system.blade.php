<?php
$category_header = $title = 'Add New System';
$menu = 'device_matrix';
?>

@extends('layouts.admin.master')

@section('title', 'Add New System')

@section('styles')
<style>
    .badge-environment {
        font-size: 14px;
        padding: 5px 10px;
        margin-left: 10px;
    }
    .badge-production {
        background-color: #dc3545;
        color: white;
    }
    .badge-uat {
        background-color: #28a745;
        color: white;
    }
    }
    .btn-sm {
        color: white !important;
    }
</style>
@endsection

@section('content')
<div id="content_hldr">
    <div id="cat_title_hldr">
        <div id="cat_title">
            <div id="end_cap">
                <h1>
                    Add New System
                    @if($environment === 'production')
                    <span class="badge badge-environment badge-production">Production</span>
                    @else
                    <span class="badge badge-environment badge-uat">UAT/Staging</span>
                    @endif
                </h1>
            </div>
        </div>
    </div>
    
    <div id="content">
        @include('admin/software/includes/sidebar')
        
        <div id="pagecontent">
            <div id="main">
                @include('flash::message')
                
                <div class="alert alert-info">
                    <p><strong>Note:</strong> This will create a new system ID that can be used in the device matrix. After creating the system, you will be redirected to the device matrix entry form.</p>
                </div>
                
                <form action="{{ route('device.matrix.store-system') }}" method="POST">
                    @csrf
                    <input type="hidden" name="environment" value="{{ $environment }}">
                    
                    <!-- System Information Section -->
                    <div class="form-section">
                        <h4>System Information</h4>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="system_id" class="required">System ID</label>
                                    <input type="text" name="system_id" id="system_id" class="form-control @error('system_id') is-invalid @enderror" value="{{ old('system_id') }}" required>
                                    <small class="form-text text-muted">Enter a unique identifier for this system.</small>
                                    @error('system_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="system_name" class="required">System Name</label>
                                    <input type="text" name="system_name" id="system_name" class="form-control @error('system_name') is-invalid @enderror" value="{{ old('system_name') }}" required>
                                    <small class="form-text text-muted">Enter a descriptive name for this system.</small>
                                    @error('system_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="description">Description</label>
                                    <textarea name="description" id="description" class="form-control @error('description') is-invalid @enderror" rows="3">{{ old('description') }}</textarea>
                                    <small class="form-text text-muted">Optional: Provide additional details about this system.</small>
                                    @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Form Actions -->
                    <div class="btn-toolbar">
                        <button type="submit" class="btn btn-primary">Create System</button>
                        <a href="{{ route('device.matrix.create', ['environment' => $environment]) }}" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
        <div id="return"><a href="#top">top</a></div>
    </div>
</div>
<div id="content_hldr_btm"></div>
@endsection