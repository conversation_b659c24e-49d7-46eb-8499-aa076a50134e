<?php

use App\Mail\MailableTemplate;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

/** ------------------------------------------
 *  Route constraint patterns
 *  ------------------------------------------.
 */
/*

        Route::pattern('comment', '[0-9]+');
        Route::pattern('post', '[0-9]+');
        Route::pattern('user', '[0-9]+');
        Route::pattern('role', '[0-9]+');
        Route::pattern('token', '[0-9a-z]+');

    */
Route::pattern('category', '[0-9a-z-]+');
Route::pattern('category_id', '[0-9]+');
Route::pattern('id', '[0-9]+');
Route::pattern('product', '[0-9a-zA-Z-]+');
Route::pattern('section', '[0-9a-z-]+');
Route::pattern('shortname', '[0-9a-z-]+');
Route::pattern('program', '[0-9a-z-]+');
Route::pattern('serial_number', '[0-9a-zA-Z-]+');
Route::pattern('lot_number', '[0-9a-zA-Z]+');
Route::pattern('language', '[a-zA-Z]+');

Route::pattern('user_id', '[0-9]+');
Route::pattern('role_id', '[0-9]+');
Route::pattern('uuid', '[a-zA-Z0-9-]+');

/* ------------------------------------------
|  Maestro/Admin Routes
|-------------------------------------------
|
|--------------------------------------------------------------------------
|  Login Routes
|--------------------------------------------------------------------------
*/
Route::group(['prefix' => 'maestro'], function () {
    Route::get('signin', ['as' => 'signin', 'uses' => 'Auth\LoginController@showLoginForm']);
    Route::post('signin', ['as' => 'signin.post', 'uses' => 'Auth\LoginController@login']);
    Route::get('signout', ['as' => 'signout', 'uses' => 'Auth\LoginController@logout']);
    Route::post('signout', ['as' => 'signout.post', 'uses' => 'Auth\LoginController@logout']);
    Route::get('login', ['as' => 'login', 'uses' => 'Auth\LoginController@showLoginForm']);
    Route::get('logout', ['as' => 'logout', 'uses' => 'Auth\LoginController@logout']);
});

// Admin Group
Route::group(['prefix' => 'maestro', 'middleware' => ['auth'], 'namespace' => 'Admin'], function () {

    //     Route::get('test', function(){
    //         $devices = \DB::select('SELECT
    //             iua_system_upgrade_matrices.system_name,
    //             iua_user_serial_numbers_details.ultrasound,
    //             iua_user_serial_numbers_details.sherlock,
    //             iua_user_serial_numbers_details.shell,
    //             iua_user_serial_numbers_details.dicom,
    //             COUNT(DISTINCT iua_user_serial_numbers_details.serial_number) AS device_count
    //         FROM
    //             iua_system_upgrade_matrices
    //             INNER JOIN
    //             iua_user_serial_numbers_details
    //         ON
    //             iua_system_upgrade_matrices.ultrasound = iua_user_serial_numbers_details.ultrasound AND
    //             iua_system_upgrade_matrices.sherlock = iua_user_serial_numbers_details.sherlock AND
    //             iua_system_upgrade_matrices.shell = iua_user_serial_numbers_details.shell AND
    //             iua_system_upgrade_matrices.dicom = iua_user_serial_numbers_details.dicom
    //         WHERE
    //             YEAR(iua_user_serial_numbers_details.created_at) = YEAR(CURRENT_DATE() - INTERVAL 1 MONTH)
    //         AND
    //             MONTH(iua_user_serial_numbers_details.created_at) = MONTH(CURRENT_DATE() - INTERVAL 1 MONTH)
    //         GROUP BY
    //             iua_system_upgrade_matrices.system_name,
    //             iua_user_serial_numbers_details.ultrasound,
    //             iua_user_serial_numbers_details.sherlock,
    //             iua_user_serial_numbers_details.shell,
    //             iua_user_serial_numbers_details.dicom');
    // // dd($devices);
    //         $date = \Carbon\Carbon::now('America/Denver')->subMonth()->format('F Y');
    //         // dd($date);

    //         // try {
    //             $mail = Mail::to(['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']);

    //             $subject = 'Bard Access Systems | Imaging Upgrades Monthly Report';
    //             $data = ['devices' => $devices, 'date' => $date];
    //             $template = 'emails.imaging-upgrade-reporting';

    //             $mail->send(new MailableTemplate($template, $data, $subject));

    //     });

    // Admin Dashboard
    Route::get('/dashboard', ['as' => 'maestro.dashboard', function () {
        return view('admin.dashboard');
    }]);

    Route::get('/', ['as' => 'home', function () {
        return view('admin.dashboard');
    }]);

    // User Management
    Route::group(['prefix' => 'users', 'middleware' => ['role:superadmin']], function () {
        Route::get('/', ['as' => 'users.index', 'uses' => 'AdminUserController@index']);
        Route::get('create', ['as' => 'user.record.create', 'uses' => 'AdminUserController@create']);
        Route::post('store', ['as' => 'user.record.store', 'uses' => 'AdminUserController@store']);
        Route::get('{user_id}/password', ['as' => 'user.record.password', 'uses' => 'AdminUserController@password']);
        Route::post('{user_id}/updatepassword', ['as' => 'user.record.updatepassword', 'uses' => 'AdminUserController@updatePassword']);
        Route::post('{user_id}/chagestatus', ['as' => 'user.record.changestatus', 'uses' => 'AdminUserController@changeStatus']);
        Route::post('{user_id}/forcedelete', ['as' => 'user.record.forcedelete', 'uses' => 'AdminUserController@forceDelete']);
        Route::post('{user_id}/restore', ['as' => 'user.record.restore', 'uses' => 'AdminUserController@restore']);

        Route::get('/{user_id}/impersonate', ['as' => 'admin.users.impersonate', 'middleware' => ['role:superadmin'], 'uses' => 'AdminUserController@impersonate']);
    });
    Route::group(['prefix' => 'users'], function () {
        Route::get('{user_id}', ['as' => 'user.record.show', 'uses' => 'AdminUserController@show']);
        Route::post('{user_id}/update', ['as' => 'user.record.update', 'uses' => 'AdminUserController@update']);
        Route::get('{user_id}/password', ['as' => 'user.record.password', 'uses' => 'AdminUserController@password']);
        Route::post('{user_id}/updatepassword', ['as' => 'user.record.updatepassword', 'uses' => 'AdminUserController@updatePassword']);
    });

    // Role Management
    Route::group(['prefix' => 'roles', 'middleware' => ['role:superadmin']], function () {
        Route::get('/', ['as' => 'roles.index', 'uses' => 'RolesController@index']);
        Route::get('/create', ['as' => 'roles.create', 'uses' => 'RolesController@create']);
        Route::post('/store', ['as' => 'roles.store', 'uses' => 'RolesController@store']);
        Route::get('/{id}/edit', ['as' => 'roles.edit', 'uses' => 'RolesController@edit']);
        Route::post('/{id}/update', ['as' => 'roles.update', 'uses' => 'RolesController@update']);
        Route::post('/{id}/changestatus', ['as' => 'roles.changestatus', 'uses' => 'RolesController@changestatus']);
        Route::post('/{id}/destroy', ['as' => 'roles.destroy', 'uses' => 'RolesController@destroy']);
        Route::post('/{id}/restore', ['as' => 'roles.restore', 'uses' => 'RolesController@restore']);
    });

    // Permissions Management
    Route::group(['prefix' => 'permissions', 'middleware' => ['role:superadmin']], function () {
        Route::get('/', ['as' => 'permissions.index', 'uses' => 'PermissionController@index']);
        Route::get('/create', ['as' => 'permissions.create', 'uses' => 'PermissionController@create']);
        Route::post('/store', ['as' => 'permissions.store', 'uses' => 'PermissionController@store']);
        Route::get('/{id}/edit', ['as' => 'permissions.edit', 'uses' => 'PermissionController@edit']);
        Route::post('/{id}/update', ['as' => 'permissions.update', 'uses' => 'PermissionController@update']);
        Route::post('/{id}/changestatus', ['as' => 'permissions.changestatus', 'uses' => 'PermissionsController@changestatus']);
        Route::post('/{id}/destroy', ['as' => 'permissions.destroy', 'uses' => 'PermissionController@destroy']);
        Route::post('/{id}/restore', ['as' => 'permissions.restore', 'uses' => 'PermissionController@restore']);
    });

    // Categories
    // Route::resource('site_categories', 'SiteCategoriesController');
    // Route::resource('site_subcategories', 'SiteSubcategoriesController');

    /* ------------------------------------------
        /** Site Management- Categories, Subcategories, pages */

    Route::group(['prefix' => 'site', 'namespace' => 'Site', 'middleware' => ['role:superadmin']], function () {
        Route::post('page/get/subcategories/category/{category_id}', ['as' => 'site.subcategories.get', 'uses' => 'SiteSubcategoriesController@getCategorySubCategories']);

        Route::group(['prefix' => 'page'], function () {

            // Return main products categories page
            Route::get('/', ['as' => 'site.pages', 'uses' => 'SitePagesController@index']);
            Route::get('/create', ['as' => 'site.page.create', 'uses' => 'SitePagesController@create']);
            Route::post('/create', ['as' => 'site.page.store', 'uses' => 'SitePagesController@store']);
            Route::get('/{id}/edit', ['as' => 'site.page.edit', 'uses' => 'SitePagesController@edit']);
            Route::post('/{id}/edit', ['as' => 'site.page.update', 'uses' => 'SitePagesController@update']);
            Route::post('/{id}/changestatus', ['as' => 'site.page.changestatus', 'uses' => 'SitePagesController@changestatus']);
            // show all literature or videos by page
            Route::get('/{id}/literature', ['as' => 'site.page.literature', 'uses' => 'SitePagesController@literature']);
            Route::get('/{id}/video', ['as' => 'site.page.videos', 'uses' => 'SitePagesController@videos']);

            // Destroy
            Route::post('/{id}/destroy', ['as' => 'site.page.destroy', 'uses' => 'SitePagesController@destroy']);
        });

        Route::group(['prefix' => 'literature'], function () {

            // Return main products categories page
            Route::get('/', ['as' => 'site.literature', 'uses' => 'SiteLiteratureController@index']);
            Route::get('/create', ['as' => 'site.literature.create', 'uses' => 'SiteLiteratureController@create']);
            Route::post('/create', ['as' => 'site.literature.store', 'uses' => 'SiteLiteratureController@store']);
            Route::get('/{id}/edit', ['as' => 'site.literature.edit', 'uses' => 'SiteLiteratureController@edit']);
            Route::post('/{id}/edit', ['as' => 'site.literature.update', 'uses' => 'SiteLiteratureController@update']);
            Route::post('/{id}/changestatus', ['as' => 'site.literature.changestatus', 'uses' => 'SiteLiteratureController@changestatus']);
            // Destroy
            Route::post('/{id}/destroy', ['as' => 'site.literature.destroy', 'uses' => 'SiteLiteratureController@destroy']);
            Route::post('/{id}/destroy/file', ['as' => 'site.literature.destroy.file', 'uses' => 'SiteLiteratureController@destroyFile']);
            // Upload
            Route::get('/{id}/upload', ['as' => 'site.literature.upload', 'uses' => 'SiteLiteratureController@upload']);
            Route::match(['GET', 'POST'], '/{id}/storeupload', ['as' => 'site.literature.storeupload', 'uses' => 'SiteLiteratureController@storeupload']);

            Route::get('/{id}/getfiles', ['as' => 'site.literature.getfiles', 'uses' => 'SiteLiteratureController@getfiles']);
        });

        Route::group(['prefix' => 'video'], function () {

            // Return main products categories page
            Route::get('/', ['as' => 'site.video', 'uses' => 'SiteVideosController@index']);
            Route::get('/create', ['as' => 'site.video.create', 'uses' => 'SiteVideosController@create']);
            Route::post('/create', ['as' => 'site.video.store', 'uses' => 'SiteVideosController@store']);
            Route::get('/{id}/edit', ['as' => 'site.video.edit', 'uses' => 'SiteVideosController@edit']);
            Route::post('/{id}/edit', ['as' => 'site.video.update', 'uses' => 'SiteVideosController@update']);
            Route::post('/{id}/change/status', ['as' => 'site.video.changestatus', 'uses' => 'SiteVideosController@changestatus']);
            // Destroy
            Route::post('/{id}/destroy', ['as' => 'site.video.destroy', 'uses' => 'SiteVideosController@destroy']);
            Route::post('/{id}/destroy/file', ['as' => 'site.video.destroy.file', 'uses' => 'SiteVideosController@destroyFile']);
            // Upload
            Route::get('/{id}/upload', ['as' => 'site.video.upload', 'uses' => 'SiteVideosController@upload']);
            Route::match(['GET', 'POST'], '/{id}/storeupload', ['as' => 'site.video.storeupload', 'uses' => 'SiteVideosController@storeupload']);
            Route::get('/{id}/getfiles', ['as' => 'site.video.getfiles', 'uses' => 'SiteVideosController@getFiles']);
            // Create Poster and Thumbnail images
            Route::get('/{id}/poster', ['as' => 'site.video.poster', 'uses' => 'SiteVideosController@poster']);
            Route::get('/{id}/create/poster/{time}', ['as' => 'site.video.create.poster', 'uses' => 'SiteVideosController@createPoster']);
        });
    });

    /* ------------------------------------------
        /** Product Catalog Manager */

    Route::group(['prefix' => 'catalog', 'namespace' => 'ProductCatalog', 'middleware' => ['role:superadmin']], function () {

        // Return main products categories page
        Route::get('/', ['as' => 'catalog.administration', 'uses' => 'ProductCatalogController@index']);

        // use Category name
        Route::get('/{id}', ['as' => 'catalog.category', 'uses' => 'ProductCatalogController@category']);

        // Product codes
        Route::get('/category/{id}/product/create', ['as' => 'catalog.product.create', 'uses' => 'ProductController@create']);
        Route::post('/category/{id}/product/create', ['as' => 'catalog.product.store', 'uses' => 'ProductController@store']);
        Route::get('/product/{id}/edit', ['as' => 'catalog.product', 'uses' => 'ProductController@edit']);
        Route::post('/product/{id}/edit', ['as' => 'catalog.product.update', 'uses' => 'ProductController@update']);
        Route::get('/product/{id}/delete', ['as' => 'catalog.product.delete', 'uses' => 'ProductController@destroy']);
        Route::get('product/codes/update', ['as' => 'catalog.product.codes.update', 'uses' => 'ProductController@UpdateProductCodes']);

        // Field routes
        Route::get('/category/{category}/field/create', ['as' => 'catalog.field.create', 'uses' => 'FieldController@create']);
        Route::post('/category/{category}/field/create', ['as' => 'catalog.field.store', 'uses' => 'FieldController@store']);
        Route::get('/field/{id}/edit', ['as' => 'catalog.field', 'uses' => 'FieldController@edit']);
        Route::post('/field/{id}/edit', ['as' => 'catalog.field.update', 'uses' => 'FieldController@update']);
        Route::get('/field/{id}/delete', ['as' => 'catalog.field.delete', 'uses' => 'FieldController@destroy']);

        // Regenerate all the Javascript catalog files
        Route::get('/generate/all', ['as' => 'catalog.generate.all', 'uses' => 'ProductCatalogController@regenerateAllCatalogCategories']);
    });

    /* ------------------------------------------
        /** elabeling Management */

    Route::get('/elabeling', function () {
        return Redirect::to('/maestro/eliterature');
    });
    Route::group(['prefix' => 'eliterature', 'namespace' => 'Elabeling', 'middleware' => ['role:superadmin|eliterature manager|eliterature contributor']], function () {

        // Index -> READ list of all library files
        Route::get('/', ['as' => 'elabeling.index', 'uses' => 'ManagementController@index']);

        // Email Managment Panel
        Route::get('/emails', ['as' => 'elabeling.emails', 'uses' => 'ManagementController@emails']);
        Route::post('/emails', ['as' => 'elabeling.emails.update', 'uses' => 'ManagementController@emailsUpdate']);

        // Language Management Page
        Route::get('/languages', ['as' => 'elabeling.language.index', 'uses' => 'LanguageController@index']);
        Route::get('/languages/update/{id}', ['as' => 'elabeling.language.update', 'uses' => 'LanguageController@update']);

        // Create Record
        Route::get('/create', ['as' => 'elabeling.record.create', 'uses' => 'ManagementController@create']);
        Route::post('/create', ['as' => 'elabeling.record.store', 'uses' => 'ManagementController@store']);

        // Upload
        Route::get('/{id}/upload', ['as' => 'elabeling.record.upload', 'uses' => 'ManagementController@upload']);
        Route::get('/{id}/storeupload', ['as' => 'elabeling.record.storeupload', 'uses' => 'ManagementController@storeupload']);
        Route::post('/{id}/storeupload', ['as' => 'elabeling.record.storeupload', 'uses' => 'ManagementController@storeUpload']);

        // Update Listing
        Route::get('/{id}/edit', ['as' => 'elabeling.record.edit', 'uses' => 'ManagementController@edit']);
        Route::post('/{id}/update', ['as' => 'elabeling.record.update', 'uses' => 'ManagementController@update']);
        Route::post('/{id}/change/status', ['as' => 'elabeling.record.change.status', 'uses' => 'ManagementController@changestatus']);

        // Destroy
        Route::post('/{id}/delete', ['as' => 'elabeling.record.force.delete', 'middleware' => ['permission:eliterature delete'], 'uses' => 'ManagementController@forceDelete']);

        // Read
        Route::get('/{id}', ['as' => 'elabeling.record.show', 'uses' => 'ManagementController@show']);
        Route::get('/{id}/getfiles', ['as' => 'elabeling.record.getfiles', 'uses' => 'ManagementController@getFiles']);

        // Files
        Route::post('file/{id}/change/status', ['as' => 'elabeling.record.file.change.status', 'uses' => 'ManagementController@fileChangeStatus']);
        Route::post('file/{id}/delete', ['as' => 'elabeling.record.file.delete', 'middleware' => ['permission:eliterature delete'], 'uses' => 'ManagementController@destroyFile']);

        Route::get('/run/import', ['as' => 'elabeling.run.import', 'uses' => 'ImportController@runImport']);
    });

    // Software Lot Numbers Management
    Route::group(['prefix' => 'elabeling/software-lot-numbers', 'namespace' => 'Elabeling', 'middleware' => ['role:superadmin|eliterature manager|eliterature contributor']], function () {
        Route::get('/', ['as' => 'elabeling.software.lot.numbers.index', 'uses' => 'SoftwareLotNumbersController@index']);
        Route::get('/analytics', ['as' => 'elabeling.software.lot.numbers.analytics', 'uses' => 'SoftwareLotNumbersController@analytics']);
        Route::get('/export', ['as' => 'elabeling.software.lot.numbers.export', 'uses' => 'SoftwareLotNumbersController@export']);
        Route::post('/bulk-action', ['as' => 'elabeling.software.lot.numbers.bulk.action', 'uses' => 'SoftwareLotNumbersController@bulkAction']);
        Route::get('/create', ['as' => 'elabeling.software.lot.numbers.create', 'uses' => 'SoftwareLotNumbersController@create']);
        Route::post('/store', ['as' => 'elabeling.software.lot.numbers.store', 'uses' => 'SoftwareLotNumbersController@store']);
        Route::get('/{id}', ['as' => 'elabeling.software.lot.numbers.show', 'uses' => 'SoftwareLotNumbersController@show']);
        Route::get('/{id}/edit', ['as' => 'elabeling.software.lot.numbers.edit', 'uses' => 'SoftwareLotNumbersController@edit']);
        Route::put('/{id}', ['as' => 'elabeling.software.lot.numbers.update', 'uses' => 'SoftwareLotNumbersController@update']);
        Route::delete('/{id}', ['as' => 'elabeling.software.lot.numbers.destroy', 'uses' => 'SoftwareLotNumbersController@destroy']);
        Route::post('/{id}/change/status', ['as' => 'elabeling.software.lot.numbers.change.status', 'uses' => 'SoftwareLotNumbersController@changeStatus']);
    });

    /* ------------------------------------------
    /** Software Update Manager */

    Route::group(['prefix' => 'guidance-technologies', 'namespace' => 'GuidanceTechnologies', 'middleware' => ['role:superadmin|software update manager|software update developer']], function () {

        // Systems
        Route::group(['prefix' => 'systems'], function () {
            Route::get('/', ['as' => 'software.systems.index', 'middleware' => ['permission:software upgrade view'], 'uses' => 'SystemsController@index']);
            Route::get('create', ['as' => 'software.systems.create', 'middleware' => ['permission:software upgrade create'], 'uses' => 'SystemsController@create']);
            Route::post('create', ['as' => 'software.systems.store', 'middleware' => ['permission:software upgrade create'], 'uses' => 'SystemsController@store']);
            Route::get('{uuid}/edit', ['as' => 'software.systems.edit', 'middleware' => ['permission:software upgrade edit'], 'uses' => 'SystemsController@edit']);
            Route::post('{uuid}/edit', ['as' => 'software.systems.update', 'middleware' => ['permission:software upgrade edit'], 'uses' => 'SystemsController@update']);
            Route::post('{uuid}/delete', ['as' => 'software.systems.delete', 'middleware' => ['permission:software upgrade delete'], 'uses' => 'SystemsController@delete']);
            Route::post('{uuid}/change/status', ['as' => 'software.systems.change.status', 'middleware' => ['permission:software upgrade edit'], 'uses' => 'SystemsController@changeStatus']);
        });

        // System Models
        Route::group(['prefix' => 'system/models'], function () {
            Route::get('/', ['as' => 'software.system.models.index', 'middleware' => ['permission:software upgrade view'], 'uses' => 'SystemModelsController@index']);
            Route::get('create', ['as' => 'software.system.models.create', 'middleware' => ['permission:software upgrade create'], 'uses' => 'SystemModelsController@create']);
            Route::post('create', ['as' => 'software.system.models.store', 'middleware' => ['permission:software upgrade create'], 'uses' => 'SystemModelsController@store']);
            Route::get('{uuid}/edit', ['as' => 'software.system.models.edit', 'middleware' => ['permission:software upgrade edit'], 'uses' => 'SystemModelsController@edit']);
            Route::post('{uuid}/edit', ['as' => 'software.system.models.update', 'middleware' => ['permission:software upgrade edit'], 'uses' => 'SystemModelsController@update']);
            Route::post('{uuid}/delete', ['as' => 'software.system.models.delete', 'middleware' => ['permission:software upgrade delete'], 'uses' => 'SystemModelsController@delete']);
            Route::get('{uuid}/clone', ['as' => 'software.system.models.clone', 'middleware' => ['permission:software upgrade edit'], 'uses' => 'SystemModelsController@clone']);
            Route::post('{uuid}/change/status', ['as' => 'software.system.models.change.status', 'middleware' => ['permission:software upgrade edit'], 'uses' => 'SystemModelsController@changeStatus']);
        });

        // System Software Files
        Route::group(['prefix' => 'system/software'], function () {
            Route::get('/{uuid}', ['as' => 'software.system.files.index', 'middleware' => ['permission:software upgrade view'], 'uses' => 'SystemSoftwareVersionsController@index']);
            Route::get('{system_uuid}/create', ['as' => 'software.system.files.create', 'middleware' => ['permission:software upgrade create'], 'uses' => 'SystemSoftwareVersionsController@create']);
            Route::post('{system_uuid}/create', ['as' => 'software.system.files.store', 'middleware' => ['permission:software upgrade create'], 'uses' => 'SystemSoftwareVersionsController@store']);
            Route::get('{uuid}/edit', ['as' => 'software.system.files.edit', 'middleware' => ['permission:software upgrade edit'], 'uses' => 'SystemSoftwareVersionsController@edit']);
            Route::post('{uuid}/update', ['as' => 'software.system.files.update', 'middleware' => ['permission:software upgrade edit'], 'uses' => 'SystemSoftwareVersionsController@update']);
            Route::post('{uuid}/delete', ['as' => 'software.system.files.delete', 'middleware' => ['permission:software upgrade delete'], 'uses' => 'SystemSoftwareVersionsController@delete']);
            Route::post('{uuid}/change/status', ['as' => 'software.system.files.change.status', 'middleware' => ['permission:software upgrade edit'], 'uses' => 'SystemSoftwareVersionsController@changeStatus']);
            Route::get('{uuid}/upload', ['as' => 'software.system.files.upload', 'middleware' => ['permission:software upgrade create'], 'uses' => 'SystemSoftwareVersionsController@upload']);
            Route::post('{uuid}/upload/store', ['as' => 'software.system.files.upload.store', 'middleware' => ['permission:software upgrade create'], 'uses' => 'SystemSoftwareVersionsController@storeUpload']);
            Route::get('{uuid}/files/get', ['as' => 'software.system.files.get', 'middleware' => ['permission:software upgrade create'], 'uses' => 'SystemSoftwareVersionsController@getFiles']);
            // Files
            Route::get('file/{uuid}/download', ['as' => 'software.system.files.file.download', 'middleware' => ['permission:software upgrade delete'], 'uses' => 'SystemSoftwareVersionsController@download']);
            Route::post('file/{uuid}/delete', ['as' => 'software.system.files.file.delete', 'middleware' => ['permission:software upgrade delete'], 'uses' => 'SystemSoftwareVersionsController@destroyFile']);
        });

        // Operating System Files
        Route::group(['prefix' => 'os'], function () {
            Route::get('files/upload', ['as' => 'software.os.files.upload', 'middleware' => ['permission:software upgrade create'], 'uses' => 'OperatingSystemFilesController@upload']);
            Route::post('file/upload/store', ['as' => 'software.os.files.upload.store', 'middleware' => ['permission:software upgrade create'], 'uses' => 'OperatingSystemFilesController@storeUpload']);
            Route::get('files/get', ['as' => 'software.os.files.get', 'middleware' => ['permission:software upgrade create'], 'uses' => 'OperatingSystemFilesController@getFiles']);
            // Files
            Route::post('file/{uuid}/change/status', ['as' => 'software.os.files.file.change.status', 'middleware' => ['permission:software upgrade edit'], 'uses' => 'OperatingSystemFilesController@fileChangeStatus']);
            Route::post('file/{uuid}/set/environment/{environment}', ['as' => 'software.os.files.file.set.environment', 'uses' => 'OperatingSystemFilesController@setEnvironment']);
            Route::post('file/{uuid}/delete', ['as' => 'software.os.files.file.delete', 'middleware' => ['permission:software upgrade delete'], 'uses' => 'OperatingSystemFilesController@destroyFile']);
        });

        // System update history
        Route::group(['prefix' => 'system/update/history'], function () {
            Route::get('history/{environment?}', ['as' => 'software.system.update.history.index', 'middleware' => ['permission:software upgrade view'], 'uses' => 'SystemUpdateHistoryController@index']);
            Route::get('report/{environment?}', ['as' => 'software.system.update.history.report', 'middleware' => ['permission:software upgrade view'], 'uses' => 'SystemUpdateHistoryController@report']);
            Route::post('report/{environment?}/post', ['as' => 'software.system.update.history.report.post', 'middleware' => ['permission:software upgrade view'], 'uses' => 'SystemUpdateHistoryController@report']);
            Route::get('download/{uuid}/{filename}', ['as' => 'software.system.update.history.log.download', 'middleware' => ['permission:software upgrade view'], 'uses' => 'SystemUpdateHistoryController@download']);
            Route::post('{uuid}/delete', ['as' => 'software.system.update.history.delete', 'middleware' => ['permission:software upgrade delete'], 'uses' => 'SystemUpdateHistoryController@delete']);
        });

        // Internal Serial Numbers
        Route::group(['prefix' => 'serial/number/eligibility'], function () {
            Route::get('/', ['as' => 'software.serial.number.eligibility.index', 'middleware' => ['permission:software upgrade view'], 'uses' => 'SystemUpgradeEligibilityController@index']);
            Route::get('create', ['as' => 'software.serial.number.eligibility.create', 'middleware' => ['permission:software upgrade create'], 'uses' => 'SystemUpgradeEligibilityController@create']);
            Route::post('create', ['as' => 'software.serial.number.eligibility.store', 'middleware' => ['permission:software upgrade create'], 'uses' => 'SystemUpgradeEligibilityController@store']);
            Route::get('{uuid}/edit', ['as' => 'software.serial.number.eligibility.edit', 'middleware' => ['permission:software upgrade edit'], 'uses' => 'SystemUpgradeEligibilityController@edit']);
            Route::post('{uuid}/edit', ['as' => 'software.serial.number.eligibility.update', 'middleware' => ['permission:software upgrade edit'], 'uses' => 'SystemUpgradeEligibilityController@update']);
            Route::post('{uuid}/delete', ['as' => 'software.serial.number.eligibility.delete', 'middleware' => ['permission:software upgrade delete'], 'uses' => 'SystemUpgradeEligibilityController@delete']);
            Route::post('{uuid}/change/status', ['as' => 'software.serial.number.eligibility.change.status', 'middleware' => ['permission:software upgrade edit'], 'uses' => 'SystemUpgradeEligibilityController@changeStatus']);
        });
    });

    Route::group(['prefix' => 'guidance-technologies/import', 'middleware' => ['role:superadmin|software update manager|software update developer']], function () {
        Route::get('/', ['as' => 'software.system.country.location.import.index', 'uses' => 'ImportSpreadsheetController@index']);
        Route::post('upload', ['as' => 'software.system.country.location.import.upload', 'uses' => 'ImportSpreadsheetController@import']);
    });

    // Device Matrix Management
    Route::group(['prefix' => 'device-matrix', 'middleware' => ['role:superadmin|software update manager|software update developer']], function () {
        Route::get('/', ['as' => 'device.matrix.index', 'uses' => 'ImagingUpgrade\DeviceMatrixController@index']);
        Route::get('/create', ['as' => 'device.matrix.create', 'uses' => 'ImagingUpgrade\DeviceMatrixController@create']);
        Route::post('/store', ['as' => 'device.matrix.store', 'uses' => 'ImagingUpgrade\DeviceMatrixController@store']);
        Route::get('/edit/{id}', ['as' => 'device.matrix.edit', 'uses' => 'ImagingUpgrade\DeviceMatrixController@edit']);
        Route::put('/update/{id}', ['as' => 'device.matrix.update', 'uses' => 'ImagingUpgrade\DeviceMatrixController@update']);
        Route::delete('/destroy/{id}', ['as' => 'device.matrix.destroy', 'uses' => 'ImagingUpgrade\DeviceMatrixController@destroy']);
        Route::post('/bulk-update', ['as' => 'device.matrix.bulk-update', 'uses' => 'ImagingUpgrade\DeviceMatrixController@bulkUpdate']);
        Route::get('/export', ['as' => 'device.matrix.export', 'uses' => 'ImagingUpgrade\DeviceMatrixController@export']);
        Route::post('/copy-environment', ['as' => 'device.matrix.copy-environment', 'uses' => 'ImagingUpgrade\DeviceMatrixController@copyEnvironment']);
        
        // System and Country Management
        Route::get('/create-system', ['as' => 'device.matrix.create-system', 'uses' => 'ImagingUpgrade\DeviceMatrixController@createSystem']);
        Route::post('/store-system', ['as' => 'device.matrix.store-system', 'uses' => 'ImagingUpgrade\DeviceMatrixController@storeSystem']);
        Route::get('/create-country', ['as' => 'device.matrix.create-country', 'uses' => 'ImagingUpgrade\DeviceMatrixController@createCountry']);
        Route::post('/store-country', ['as' => 'device.matrix.store-country', 'uses' => 'ImagingUpgrade\DeviceMatrixController@storeCountry']);
    });

    // Server Info
    Route::get('/server', ['as' => 'server.index', 'uses' => 'ServerController@index']);
    Route::get('/server/errors', ['as' => 'server.errors', 'uses' => 'ServerController@errors']);
    Route::post('server/errors/{error_id}/chagestatus', ['as' => 'error.record.changestatus', 'uses' => 'ServerController@changeStatus']);
    Route::post('server/errors/{error_id}/destroy', ['as' => 'error.record.destroy', 'uses' => 'ServerController@destroy']);
    Route::post('server/errors/{error_id}/forcedelete', ['as' => 'error.record.forcedelete', 'uses' => 'ServerController@forceDelete']);
    Route::post('server/errors/{error_id}/restore', ['as' => 'error.record.restore', 'uses' => 'ServerController@restore']);

    // Artisan Commands
    Route::group(['prefix' => 'artisan'], function () {
        Route::get('/{command}/{method?}', function ($command, $method = null) {
            if ($method) {
                \Artisan::call($command.':'.$method);
                echo 'Artisan command '.$command.':'.$method.' ran successfully.';
            } else {
                \Artisan::call($command);
                echo 'Artisan command '.$command.' ran successfully.';
            }
        });
    });

    // ADMIN/MAESTRO Group End
});
