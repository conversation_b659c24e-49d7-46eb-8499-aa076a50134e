<?php

namespace App\Models\Elabeling;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ElabelingResourceTypes extends Model
{
    use SoftDeletes;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'eifu_resource_types';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'code',
        'order'
    ];

    /**
     * The dates protected and treated as Carbon instances
     *
     * @var array
     */
    protected $dates = ['deleted_at'];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [];

    /**
     * Maps the relationship between Library Records and Instruction types
     *
     * @var string
     */
    public function library_records()
    {
        return $this->hasMany('App\Models\Elabeling\LibraryRecord', 'id', 'resource_type');
    }
}
