<?php

namespace App\Models\Elabeling;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class EifuLotNumbersSoftware extends Model
{
    use SoftDeletes;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'eifu_lot_numbers_software';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'lot_number',
        'ref',
        'product_code',
        'product_name',
        'part_number',
        'resource_type',
        'manufacture_date',
        'sequence_number'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'manufacture_date' => 'date',
        'sequence_number' => 'integer'
    ];

    /**
     * The dates protected and treated as Carbon instances
     *
     * @var array
     */
    protected $dates = ['deleted_at', 'manufacture_date'];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [];

    /**
     * Scope to search by lot number
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $lotNumber
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByLotNumber($query, $lotNumber)
    {
        return $query->where('lot_number', $lotNumber);
    }

    /**
     * Scope to search by part number
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $partNumber
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByPartNumber($query, $partNumber)
    {
        return $query->where('part_number', $partNumber);
    }

    /**
     * Scope to search by product code
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string $productCode
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByProductCode($query, $productCode)
    {
        return $query->where('product_code', $productCode);
    }

    /**
     * Get formatted manufacture date
     *
     * @return string
     */
    public function getFormattedManufactureDateAttribute()
    {
        return $this->manufacture_date ? $this->manufacture_date->format('Y-m-d') : '';
    }

    /**
     * Relationship: Get related library records.
     * This assumes there's a LibraryRecord model that references software lot numbers.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function libraryRecords()
    {
        // This would link to library records that reference this software lot number
        // Assuming there's a 'software_lot_id' foreign key in the library_records table
        return $this->hasMany(\App\Models\Elabeling\LibraryRecord::class, 'software_lot_id', 'id');
    }

    /**
     * Relationship: Get related elabeling library records by part number.
     * This links to elabeling library records that share the same part number.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function elabelingRecords()
    {
        return $this->hasMany(\App\Models\Elabeling\ElabelingLibraryRecord::class, 'part_number', 'part_number');
    }

    /**
     * Relationship: Get related JDE lot numbers.
     * This links to JDE lot numbers that share the same lot number.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function jdeLotNumber()
    {
        return $this->hasOne(\App\Models\Elabeling\EifuLotNumbersJde::class, 'lot_number', 'lot_number');
    }

    /**
     * Relationship: Get related MfgPro lot numbers.
     * This links to MfgPro lot numbers that share the same lot number.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function mfgProLotNumber()
    {
        return $this->hasOne(\App\Models\Elabeling\EifuLotNumbersMfgpro::class, 'lot_number', 'lot_number');
    }

    /**
     * Relationship: Get the resource type details.
     * This links to the resource types table for additional metadata.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function resourceTypeDetails()
    {
        return $this->belongsTo(\App\Models\Elabeling\ElabelingResourceTypes::class, 'resource_type', 'id');
    }

    /**
     * Scope: Get records with related data.
     * This scope includes commonly needed relationships.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithRelations($query)
    {
        return $query->with([
            'jdeLotNumber',
            'mfgProLotNumber'
        ]);
    }

    /**
     * Get the count of related records across all relationships.
     * Optimized version that uses loaded relationships when available.
     *
     * @return array
     */
    public function getRelatedRecordsCounts()
    {
        // Use loaded relationships if available to avoid additional queries
        $hasJde = $this->relationLoaded('jdeLotNumber')
            ? !is_null($this->jdeLotNumber)
            : $this->jdeLotNumber()->exists();

        $hasMfgpro = $this->relationLoaded('mfgProLotNumber')
            ? !is_null($this->mfgProLotNumber)
            : $this->mfgProLotNumber()->exists();

        return [
            'has_jde_lot' => $hasJde,
            'has_mfgpro_lot' => $hasMfgpro,
        ];
    }

    /**
     * Check if this software lot number is referenced by other systems.
     * Optimized version that uses loaded relationships when available.
     *
     * @return bool
     */
    public function hasReferences()
    {
        $counts = $this->getRelatedRecordsCounts();
        return $counts['has_jde_lot'] || $counts['has_mfgpro_lot'];
    }

    /**
     * Get a summary of all related data for this software lot number.
     *
     * @return array
     */
    public function getRelationshipSummary()
    {
        $counts = $this->getRelatedRecordsCounts();

        return [
            'has_cross_system_links' => $counts['has_jde_lot'] || $counts['has_mfgpro_lot'],
            'is_actively_used' => $this->hasReferences(),
            'details' => $counts
        ];
    }
}
