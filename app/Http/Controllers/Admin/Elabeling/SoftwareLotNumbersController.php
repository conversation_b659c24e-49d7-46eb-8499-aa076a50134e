<?php

namespace App\Http\Controllers\Admin\Elabeling;

use App\Http\Controllers\Controller;
use App\Models\Elabeling\EifuLotNumbersSoftware;
use App\Models\Elabeling\ElabelingResourceTypes;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Validation\Rule;

class SoftwareLotNumbersController extends Controller
{
    /**
     * Display a listing of the software lot numbers.
     *
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $search = $request->get('search', '');
        $perPage = $request->get('per_page', 15);
        $status = $request->get('status', '');
        $resourceType = $request->get('resource_type', '');
        $dateFrom = $request->get('date_from', '');
        $dateTo = $request->get('date_to', '');
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        $query = EifuLotNumbersSoftware::query();

        // Include trashed records if status filter is set
        if ($status === 'inactive') {
            $query = EifuLotNumbersSoftware::onlyTrashed();
        } elseif ($status === 'all') {
            $query = EifuLotNumbersSoftware::withTrashed();
        }

        // Search functionality
        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('lot_number', 'like', "%{$search}%")
                  ->orWhere('part_number', 'like', "%{$search}%")
                  ->orWhere('product_code', 'like', "%{$search}%")
                  ->orWhere('product_name', 'like', "%{$search}%")
                  ->orWhere('ref', 'like', "%{$search}%");
            });
        }

        // Resource type filter
        if (!empty($resourceType)) {
            $query->where('resource_type', $resourceType);
        }

        // Date range filter
        if (!empty($dateFrom)) {
            $query->where('manufacture_date', '>=', $dateFrom);
        }
        if (!empty($dateTo)) {
            $query->where('manufacture_date', '<=', $dateTo);
        }

        // Sorting
        $allowedSortFields = ['lot_number', 'part_number', 'product_code', 'product_name', 'manufacture_date', 'created_at', 'updated_at'];
        if (in_array($sortBy, $allowedSortFields)) {
            $query->orderBy($sortBy, $sortOrder === 'asc' ? 'asc' : 'desc');
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $softwareLotNumbers = $query->paginate($perPage);

        // Efficiently get relationship status for all records in current page
        $lotNumbers = $softwareLotNumbers->pluck('lot_number')->toArray();
        $jdeLinks = \DB::table('eifu_lot_numbers_jde')
            ->whereIn('lot_number', $lotNumbers)
            ->whereNull('deleted_at')
            ->pluck('lot_number')
            ->toArray();
        $mfgproLinks = \DB::table('eifu_lot_numbers_mfgpro')
            ->whereIn('lot_number', $lotNumbers)
            ->whereNull('deleted_at')
            ->pluck('lot_number')
            ->toArray();

        // Add relationship status to each record
        foreach ($softwareLotNumbers as $record) {
            $record->has_jde_link = in_array($record->lot_number, $jdeLinks);
            $record->has_mfgpro_link = in_array($record->lot_number, $mfgproLinks);
        }

        // Get resource types for filter dropdown
        $resourceTypes = EifuLotNumbersSoftware::select('resource_type')
            ->whereNotNull('resource_type')
            ->where('resource_type', '!=', '')
            ->distinct()
            ->orderBy('resource_type')
            ->pluck('resource_type');

        return view('admin.elabeling.software-lot-numbers.index', compact(
            'softwareLotNumbers',
            'search',
            'perPage',
            'status',
            'resourceType',
            'dateFrom',
            'dateTo',
            'sortBy',
            'sortOrder',
            'resourceTypes'
        ));
    }

    /**
     * Show the form for creating a new software lot number.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $resourceTypes = ElabelingResourceTypes::orderBy('order')->pluck('name', 'id')->all();
        
        return view('admin.elabeling.software-lot-numbers.create', compact('resourceTypes'));
    }

    /**
     * Store a newly created software lot number in storage.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'lot_number' => 'required|string|max:25',
            'ref' => 'nullable|string|max:25',
            'product_code' => 'nullable|string|max:25',
            'product_name' => 'nullable|string|max:255',
            'part_number' => 'required|string|max:25',
            'resource_type' => 'nullable|string|max:25',
            'manufacture_date' => 'nullable|date',
            'sequence_number' => 'nullable|integer|min:0'
        ]);

        try {
            $softwareLotNumber = EifuLotNumbersSoftware::create($request->all());
            
            \Session::flash('message', 'Software lot number created successfully for lot: ' . $softwareLotNumber->lot_number);
            
            return redirect()->route('elabeling.software.lot.numbers.index');
        } catch (\Exception $e) {
            \Session::flash('error', 'Error creating software lot number: ' . $e->getMessage());
            
            return redirect()->back()->withInput();
        }
    }

    /**
     * Display the specified software lot number.
     *
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $softwareLotNumber = EifuLotNumbersSoftware::withTrashed()
            ->with(['jdeLotNumber', 'mfgProLotNumber']) // Only load relationships for detail view
            ->findOrFail($id);

        return view('admin.elabeling.software-lot-numbers.show', compact('softwareLotNumber'));
    }

    /**
     * Show the form for editing the specified software lot number.
     *
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $softwareLotNumber = EifuLotNumbersSoftware::findOrFail($id);
        $resourceTypes = ElabelingResourceTypes::orderBy('order')->pluck('name', 'id')->all();
        
        return view('admin.elabeling.software-lot-numbers.edit', compact('softwareLotNumber', 'resourceTypes'));
    }

    /**
     * Update the specified software lot number in storage.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $softwareLotNumber = EifuLotNumbersSoftware::findOrFail($id);
        
        $request->validate([
            'lot_number' => 'required|string|max:25',
            'ref' => 'nullable|string|max:25',
            'product_code' => 'nullable|string|max:25',
            'product_name' => 'nullable|string|max:255',
            'part_number' => 'required|string|max:25',
            'resource_type' => 'nullable|string|max:25',
            'manufacture_date' => 'nullable|date',
            'sequence_number' => 'nullable|integer|min:0'
        ]);

        try {
            $softwareLotNumber->update($request->all());
            
            \Session::flash('message', 'Software lot number updated successfully for lot: ' . $softwareLotNumber->lot_number);
            
            return redirect()->route('elabeling.software.lot.numbers.index');
        } catch (\Exception $e) {
            \Session::flash('error', 'Error updating software lot number: ' . $e->getMessage());
            
            return redirect()->back()->withInput();
        }
    }

    /**
     * Remove the specified software lot number from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        try {
            $softwareLotNumber = EifuLotNumbersSoftware::findOrFail($id);
            $lotNumber = $softwareLotNumber->lot_number;
            
            $softwareLotNumber->delete();
            
            \Session::flash('message', 'Software lot number deleted successfully for lot: ' . $lotNumber);
            
            return redirect()->route('elabeling.software.lot.numbers.index');
        } catch (\Exception $e) {
            \Session::flash('error', 'Error deleting software lot number: ' . $e->getMessage());
            
            return redirect()->back();
        }
    }

    /**
     * Change the status of the specified software lot number.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function changeStatus(Request $request, $id)
    {
        try {
            $softwareLotNumber = EifuLotNumbersSoftware::withTrashed()->findOrFail($id);

            if ($softwareLotNumber->trashed()) {
                $softwareLotNumber->restore();
                $status = 'activated';
            } else {
                $softwareLotNumber->delete();
                $status = 'deactivated';
            }

            return response()->json([
                'success' => true,
                'message' => "Software lot number {$status} successfully",
                'status' => $status
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error changing status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display analytics dashboard for software lot numbers.
     *
     * @return \Illuminate\View\View
     */
    public function analytics()
    {
        try {
            // Basic metrics
            $totalLots = EifuLotNumbersSoftware::withTrashed()->count();
            $activeLots = EifuLotNumbersSoftware::count();
            $inactiveLots = EifuLotNumbersSoftware::onlyTrashed()->count();
            $recentAdditions = EifuLotNumbersSoftware::where('created_at', '>=', now()->subDays(30))->count();

        // Resource type distribution
        $resourceTypeDistribution = EifuLotNumbersSoftware::select('resource_type')
            ->selectRaw('count(*) as count')
            ->whereNotNull('resource_type')
            ->groupBy('resource_type')
            ->orderBy('count', 'desc')
            ->get();

        // Monthly trends (last 12 months)
        $monthlyTrends = EifuLotNumbersSoftware::selectRaw('
                YEAR(created_at) as year,
                MONTH(created_at) as month,
                MONTHNAME(created_at) as month_name,
                count(*) as count
            ')
            ->where('created_at', '>=', now()->subMonths(12))
            ->groupBy('year', 'month', 'month_name')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get();

        // Recent activity (last 10 records)
        $recentActivity = EifuLotNumbersSoftware::withTrashed()
            ->whereNotNull('updated_at')
            ->where('updated_at', '!=', '')
            ->where('updated_at', '>', '1970-01-01 00:00:00')
            ->orderBy('updated_at', 'desc')
            ->limit(10)
            ->get();

        // Top product codes
        $topProductCodes = EifuLotNumbersSoftware::select('product_code')
            ->selectRaw('count(*) as count')
            ->whereNotNull('product_code')
            ->where('product_code', '!=', '')
            ->groupBy('product_code')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get();

        $metrics = [
            'total_lots' => $totalLots,
            'active_lots' => $activeLots,
            'inactive_lots' => $inactiveLots,
            'recent_additions' => $recentAdditions,
            'active_percentage' => $totalLots > 0 ? round(($activeLots / $totalLots) * 100, 1) : 0,
            'resource_type_distribution' => $resourceTypeDistribution,
            'monthly_trends' => $monthlyTrends,
            'recent_activity' => $recentActivity,
            'top_product_codes' => $topProductCodes
        ];

            return view('admin.elabeling.software-lot-numbers.analytics', compact('metrics'));

        } catch (\Exception $e) {
            \Log::error('Analytics error: ' . $e->getMessage());

            // Return with default empty metrics on error
            $metrics = [
                'total_lots' => 0,
                'active_lots' => 0,
                'inactive_lots' => 0,
                'recent_additions' => 0,
                'active_percentage' => 0,
                'resource_type_distribution' => collect(),
                'monthly_trends' => collect(),
                'recent_activity' => collect(),
                'top_product_codes' => collect()
            ];

            return view('admin.elabeling.software-lot-numbers.analytics', compact('metrics'))
                ->with('error', 'Unable to load analytics data. Please try again later.');
        }
    }

    /**
     * Export software lot numbers based on current filters.
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function export(Request $request)
    {
        $format = $request->get('format', 'csv');
        $query = $this->buildSearchQuery($request);

        switch ($format) {
            case 'excel':
                return $this->exportExcel($query);
            case 'pdf':
                return $this->exportPdf($query);
            default:
                return $this->exportCsv($query);
        }
    }

    /**
     * Build search query based on request parameters.
     *
     * @param Request $request
     * @return \Illuminate\Database\Eloquent\Builder
     */
    private function buildSearchQuery(Request $request)
    {
        $search = $request->get('search', '');
        $status = $request->get('status', '');
        $resourceType = $request->get('resource_type', '');
        $dateFrom = $request->get('date_from', '');
        $dateTo = $request->get('date_to', '');
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        $query = EifuLotNumbersSoftware::query();

        // Include trashed records if status filter is set
        if ($status === 'inactive') {
            $query = EifuLotNumbersSoftware::onlyTrashed();
        } elseif ($status === 'all') {
            $query = EifuLotNumbersSoftware::withTrashed();
        }

        // Search functionality
        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('lot_number', 'like', "%{$search}%")
                  ->orWhere('part_number', 'like', "%{$search}%")
                  ->orWhere('product_code', 'like', "%{$search}%")
                  ->orWhere('product_name', 'like', "%{$search}%")
                  ->orWhere('ref', 'like', "%{$search}%");
            });
        }

        // Resource type filter
        if (!empty($resourceType)) {
            $query->where('resource_type', $resourceType);
        }

        // Date range filter
        if (!empty($dateFrom)) {
            $query->where('manufacture_date', '>=', $dateFrom);
        }
        if (!empty($dateTo)) {
            $query->where('manufacture_date', '<=', $dateTo);
        }

        // Sorting
        $allowedSortFields = ['lot_number', 'part_number', 'product_code', 'product_name', 'manufacture_date', 'created_at', 'updated_at'];
        if (in_array($sortBy, $allowedSortFields)) {
            $query->orderBy($sortBy, $sortOrder === 'asc' ? 'asc' : 'desc');
        } else {
            $query->orderBy('created_at', 'desc');
        }

        return $query;
    }

    /**
     * Export data as CSV.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    private function exportCsv($query)
    {
        $filename = 'software-lot-numbers-' . date('Y-m-d-H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        return response()->stream(function () use ($query) {
            $handle = fopen('php://output', 'w');

            // Add CSV headers
            fputcsv($handle, [
                'Lot Number',
                'Part Number',
                'Product Code',
                'Product Name',
                'Reference',
                'Resource Type',
                'Manufacture Date',
                'Sequence Number',
                'Status',
                'Created Date',
                'Modified Date'
            ]);

            // Add data rows
            $query->chunk(1000, function ($records) use ($handle) {
                foreach ($records as $record) {
                    fputcsv($handle, [
                        $record->lot_number,
                        $record->part_number,
                        $record->product_code,
                        $record->product_name,
                        $record->ref,
                        $record->resource_type,
                        $record->manufacture_date ? $record->manufacture_date->format('Y-m-d') : '',
                        $record->sequence_number,
                        $record->deleted_at ? 'Inactive' : 'Active',
                        $record->created_at ? $record->created_at->format('Y-m-d H:i:s') : '',
                        $record->updated_at ? $record->updated_at->format('Y-m-d H:i:s') : ''
                    ]);
                }
            });

            fclose($handle);
        }, 200, $headers);
    }

    /**
     * Export data as Excel (basic implementation).
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    private function exportExcel($query)
    {
        // For now, return CSV with Excel MIME type
        // In a full implementation, you would use a package like PhpSpreadsheet
        $filename = 'software-lot-numbers-' . date('Y-m-d-H-i-s') . '.xlsx';

        $headers = [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        return $this->exportCsv($query);
    }

    /**
     * Export data as PDF (basic implementation).
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Http\Response
     */
    private function exportPdf($query)
    {
        $data = $query->get();
        $filename = 'software-lot-numbers-' . date('Y-m-d-H-i-s') . '.pdf';

        // For now, return a simple HTML response
        // In a full implementation, you would use a package like DomPDF or wkhtmltopdf
        $html = view('admin.elabeling.software-lot-numbers.export-pdf', compact('data'))->render();

        return response($html)
            ->header('Content-Type', 'text/html')
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    }

    /**
     * Handle bulk operations on software lot numbers.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,delete,export',
            'selected_ids' => 'required|array|min:1',
            'selected_ids.*' => 'integer|exists:eifu_lot_numbers_software,id'
        ]);

        $action = $request->get('action');
        $ids = $request->get('selected_ids', []);
        $count = count($ids);

        try {
            switch ($action) {
                case 'activate':
                    EifuLotNumbersSoftware::withTrashed()->whereIn('id', $ids)->restore();
                    $message = "Successfully activated {$count} software lot number(s)";
                    break;

                case 'deactivate':
                    EifuLotNumbersSoftware::whereIn('id', $ids)->delete();
                    $message = "Successfully deactivated {$count} software lot number(s)";
                    break;

                case 'delete':
                    EifuLotNumbersSoftware::whereIn('id', $ids)->forceDelete();
                    $message = "Successfully permanently deleted {$count} software lot number(s)";
                    break;

                case 'export':
                    return $this->exportSelected($ids);

                default:
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid action specified'
                    ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'count' => $count
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error performing bulk action: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export selected records.
     *
     * @param array $ids
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    private function exportSelected($ids)
    {
        $query = EifuLotNumbersSoftware::withTrashed()->whereIn('id', $ids);
        return $this->exportCsv($query);
    }
}
